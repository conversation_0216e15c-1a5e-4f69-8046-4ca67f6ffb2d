/**
 * Check required environment variables
 */
'use strict'

// Check if chalk is available, if not use simple console.log
let chalk
try {
  chalk = require('chalk')
} catch (e) {
  // If chalk is not available, create a simple replacement
  chalk = {
    blue: (text) => text,
    green: (text) => text,
    red: (text) => text,
    yellow: (text) => text,
    gray: (text) => text
  }
}

// Define environment variables required for different environments
const REQUIRED_ENV_VARS = {
  // Environment variables required for development environment
  development: [
    // 'VUE_APP_LOCALHOST',
    // 'VUE_APP_LOCAL_PROTOCOL',
    'VUE_APP_PROXY_PATH',
    'VUE_APP_PROXY_TARGET',
    'VUE_APP_BASE_API',
    'VUE_APP_PROTOCOL',
    'VUE_APP_IP',
    'VUE_APP_PORT',
    'VUE_APP_PROJECT_ROOT',
    'VUE_APP_REMOTE_PROJECT_NAME',
    'VUE_APP_URI',
    'VUE_APP_RPT_PROTOCOL',
    'VUE_APP_RPT_IP',
    'VUE_APP_RPT_PORT',
    'VUE_APP_RPT_URL',
    'VUE_APP_WEB_CODE',
    'VUE_APP_TUTORIAL_PATH'
  ],
  // 生產環境必需的環境變量
  production: [
    'VUE_APP_BASE_API',
    'VUE_APP_ENV_CONFIG',
    'VUE_APP_PROTOCOL',
    'VUE_APP_IP',
    'VUE_APP_PORT',
    'VUE_APP_PROJECT_ROOT',
    'VUE_APP_REMOTE_PROJECT_NAME',
    'VUE_APP_URI',
    'VUE_APP_RPT_PROTOCOL',
    'VUE_APP_RPT_IP',
    'VUE_APP_RPT_PORT',
    'VUE_APP_RPT_URL',
    'VUE_APP_WEB_CODE',
    'VUE_APP_TUTORIAL_PATH',
    'VUE_APP_SERVER_NAME'
  ]
}

function checkEnvironmentVariables() {
  // 加載環境變量
  const { loadEnvironment, getEnvironmentInfo } = require('../config/load-env')
  loadEnvironment()

  const envInfo = getEnvironmentInfo()
  const { nodeEnv } = envInfo

  console.log(chalk.blue(`檢查 ${nodeEnv} 環境的環境變量...`))

  // 根據環境選擇需要檢查的變量
  let requiredVars = REQUIRED_ENV_VARS[nodeEnv] || REQUIRED_ENV_VARS.development

  const missingVars = []
  const presentVars = []

  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      missingVars.push(varName)
    } else {
      presentVars.push(varName)
    }
  })

  // 顯示檢查結果
  if (presentVars.length > 0) {
    console.log(chalk.green(`✓ 找到 ${presentVars.length} 個環境變量:`))
    presentVars.forEach(varName => {
      const value = process.env[varName]
      const displayValue = value.length > 50 ? value.substring(0, 50) + '...' : value
      console.log(chalk.gray(`  ${varName}=${displayValue}`))
    })
  }

  if (missingVars.length > 0) {
    console.log(chalk.red(`✗ 缺少 ${missingVars.length} 個必需的環境變量:`))
    missingVars.forEach(varName => {
      console.log(chalk.red(`  ${varName}`))
    })

    console.log(chalk.yellow('\n請檢查以下文件是否存在並包含所需的環境變量:'))
    if (nodeEnv === 'development') {
      console.log(chalk.yellow('  .env.development'))
    } else {
      console.log(chalk.yellow('  .env'))
    }

    console.log(chalk.yellow('\n參考 .env.example 文件查看所需的環境變量格式'))

    process.exit(1)
  }

  console.log(chalk.green('✓ 所有必需的環境變量檢查通過\n'))
}

module.exports = checkEnvironmentVariables

// 如果直接運行此腳本，則執行檢查
if (require.main === module) {
  checkEnvironmentVariables()
}
