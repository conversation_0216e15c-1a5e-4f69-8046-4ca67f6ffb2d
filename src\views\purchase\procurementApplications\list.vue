<template>
  <div class="page-procurement-applications-list">
    <div ref="header" class="page-header">
      <div class="filters">
        <el-form :inline="true" class="mini-form">
          <el-form-item :label="t('year')">
            <el-select
              v-model="preferences.filters.year"
              class="input input-select"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_code"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('applyDate')">
            <el-date-picker
              v-model="preferences.filters.beginDate"
              :value-format="dateFormatStr"
              class="input input-date"
              type="date"
              @change="fetchData"
            />
          </el-form-item>
          <el-form-item label="-" label-width="1">
            <el-date-picker
              v-model="preferences.filters.endDate"
              :value-format="dateFormatStr"
              class="input input-date"
              type="date"
              @change="fetchData"
            />
          </el-form-item>
          <el-form-item :label="t('budgetGroup')">
            <budget-select-tree-vue
              :select-all="true"
              :data="budgetGroupList"
              :budget-id.sync="preferences.filters.budgetId"
              :budget-code.sync="preferences.filters.budgetCode"
              :language="language"
              :disabled-method="budgetDisabledMethod"
              style="width: 300px"
              @change="onChangeBudgetGroup"
            />
            <!--            <el-select v-model="preferences.filters.budgetCode" class="input input-select">-->
            <!--              <el-option value="" label="-"/>-->

            <!--              <el-option-->
            <!--                v-for="item in budgetGroupList"-->
            <!--                :key="item.budget_id"-->
            <!--                :label="item[isEnglish ? 'name_en' : 'name_cn']"-->
            <!--                :value="item.budget_id"-->
            <!--              />-->
            <!--            </el-select>-->
          </el-form-item>
          <br>
          <el-form-item :label="t('level')">
            <!--            <el-input class="input input-select"/>-->
            <el-select
              v-model="preferences.filters.levelCode"
              class="input input-select"
              clearable
              @change="fetchData"
            >
              <el-option value="" label="-" />
              <!--              <el-option-->
              <!--                v-for="item in categoryList"-->
              <!--                :key="item.value"-->
              <!--                :label="item.label"-->
              <!--                :value="item.value"-->
              <!--              />-->
              <el-option
                v-for="item in levels"
                :key="item.pro_level_id"
                :label="item[isEnglish ? 'pro_level_name_en' : 'pro_level_name_cn']"
                :value="item.pro_level_code"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('status')">
            <el-select
              v-model="preferences.filters.status"
              class="input input-select"
              @change="fetchData"
            >
              <el-option value="" label="-" />
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('applyStaff')">
            <el-select
              v-model="preferences.filters.staff"
              class="input input-select"
              @change="fetchData"
            >
              <el-option value="" label="-" />
              <el-option
                v-for="item in staffs"
                :key="item.staff_id"
                :label="item[isEnglish ? 'st_name_en' : 'st_name_cn']"
                :value="item.staff_id"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('filterRemark')">
            <el-input
              v-model="preferences.filters.title_or_remark"
              class="input input-select"
              clearable
              @keyup.native.enter="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="actions-icon">
        <i class="edac-icon action-icon edac-icon-add1" @click="onAdd" />
        <i class="edac-icon action-icon edac-icon-setting1" @click="onSetting" />
      </div>
    </div>
    <div :style="tableHeight" class="content">
      <ETable
        ref="table"
        v-loading="!loading && tableLoading"
        :data="tableData"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :show-index="false"
        :show-actions="true"
        :actions-min-width="5"
        :show-checkbox="false"
        :default-top="230"
        :auto-height="false"
        :filter-class-function="filterClassFunction"
        height="100%"
        action-label=" "
        border
        @changeWidth="changeColumnWidth"
      >
        <template slot="columns">
          <el-table-column
            v-for="item in columns"
            :key="item.ss_key"
            :label="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :class-name="item.ss_key + ' mini-form'"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :column-key="item.ss_key"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span v-if="item.ss_key === 'status'">
                <el-tag :type="tagNames[scope.row.status]" size="mini">{{
                  statusFormat(scope.row.status)
                }}</el-tag>
              </span>
              <span v-else>
                {{
                  $refs.table.customFormatter(
                    item.ss_key,
                    scope.row[$refs.table.column_property(item)]
                  )
                }}
              </span>
            </template>
          </el-table-column>
        </template>
        <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
          <div class="operation_icon">
            <i class="el-icon-view" @click="onView(scope.row)" />
            <i
              v-if="hasPermission_Edit"
              :class="canEdit(scope.row) ? 'el-icon-edit' : 'el-icon-edit disabled'"
              @click="onEdit(scope.row)"
            />
            <i
              v-if="hasPermission_Delete"
              :class="canDelete(scope.row) ? 'el-icon-close' : 'el-icon-close disabled'"
              @click="onDelete(scope.row)"
            />
          </div>
        </template>
      </ETable>
      <div class="pagination">
        <el-pagination
          :current-page.sync="page"
          :page-sizes="[30, 50, 100]"
          :page-size.sync="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </div>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      table-type="full-screen-without-first-field"
      @reloadStyleSheets="loadUserStyle"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears } from '@/api/master/years'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import mixinPermission from '@/views/mixins/permission'
import { fetchStaffs } from '@/api/assistance/staff'
import { fetchProcurementLevels } from '@/api/purchase/procurementLevels'
import ETable from '@/components/ETable/index'
import { fetchProcurementApplications } from '@/api/purchase/procurementApplications'
import { fetchBudgetGroupList } from '@/api/budget'
import BudgetSelectTreeVue from '@/views/budget/common/BudgetSelectTreeVue'
import { listenTo } from '@/utils/resizeListen'

let groupCount = 1

export default {
  name: 'ProcurementApplicationsList',
  components: { BudgetSelectTreeVue, ETable, customStyle },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission],
  data() {
    return {
      loading: true,
      tableLoading: true,
      showDialog: false,
      langKey: 'purchase.daily.procurementApplications.',
      tableData: [],
      preferences: {
        filters: {
          year: '',
          beginDate: '',
          endDate: '',
          budgetId: '',
          budgetCode: '',
          levelCode: '',
          status: '',
          staff: '',
          pro_no: '',
          title_or_remark: '',
        },
      },
      page: 1,
      pageSize: 30,
      total: 0,

      years: [],
      staffs: [],
      levels: [],
      budgetGroupList: [],
      tableColumns: [
        'g_budget_name_',
        'pro_no',
        'pro_title',
        'apply_date',
        'invite_date',
        'applicant_name_',
        'r_staff_name_',
        'a_staff_name_',
        'status',
      ],
      amountColumns: [],

      resizeListen: {},
      headerHeight: 70,
    }
  },
  computed: {
    ...mapGetters([]),
    statusList() {
      return [
        { value: 'A', label: this.$t('purchase.status.A') },
        { value: 'G', label: this.$t('purchase.status.G') },
        { value: 'GR', label: this.$t('purchase.status.GR') },
        { value: 'GA', label: this.$t('purchase.status.GA') },
        { value: 'I', label: this.$t('purchase.status.I') },
        { value: 'K', label: this.$t('purchase.status.K') },
        { value: 'M', label: this.$t('purchase.status.M') },
        { value: 'MR', label: this.$t('purchase.status.MR') },
        { value: 'MI', label: this.$t('purchase.status.MI') },
        { value: 'MA', label: this.$t('purchase.status.MA') },
        { value: 'O', label: this.$t('purchase.status.O') },
      ]
    },
    tagNames() {
      return {
        A: 'info',
        G: 'info',
        GR: 'danger',
        GA: 'warning',
        I: 'info',
        K: 'info',
        M: 'info',
        MR: 'danger',
        MI: 'warning',
        MA: 'warning',
        O: 'success',
      }
    },
    categoryList() {
      return [
        { value: 'P', label: this.$t('purchase.category.P') },
        { value: 'S', label: this.$t('purchase.category.S') },
      ]
    },
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    tableHeight() {
      return `height: calc(100% - ${this.headerHeight}px - 30px);`
    },
  },
  created() {
    this.saveUserLastPage()
    this.init()
  },
  mounted() {
    this.$nextTick(() => {
      this.resizeListen = listenTo(this.$refs.header, ({ width, height, ele }) => {
        this.headerHeight = height + 10
      })
    })
  },
  methods: {
    t(key) {
      return this.$t(this.langKey + key)
    },
    async init() {
      try {
        this.years = await fetchYears()
        await this.loadUserPreference()
        if (!this.preferences.filters.year) {
          if (this.years.length > 0) {
            this.preferences.filters.year = this.years[0].fy_code
          } else {
            return
          }
        }
        // 獲取年度
        const fy_code = this.preferences.filters.year
        // const st_grade = 'S'
        // 獲取職員
        this.staffs = await fetchStaffs({ fy_code })
        // 獲取採購等級
        this.levels = await fetchProcurementLevels()

        // const budget_types = 'F,G'
        // this.budgetGroupList = await fetchBudgetGroupList({ fy_code, budget_types })
        await this.fetchBudgetList()
      } catch (e) {
        console.log(e)
        return
      }
      try {
        //
        await this.fetchData()
      } catch (e) {
        return
      }
    },
    async fetchBudgetList() {
      // 獲取年度
      const fy_code = this.preferences.filters.year
      const budget_types = 'F,G'
      this.budgetGroupList = await fetchBudgetGroupList({ fy_code, budget_types })
    },
    async fetchData() {
      try {
        const filters = this.preferences.filters
        const fy_code = filters.year
        const pro_no = undefined
        const apply_begin_date = filters.beginDate
        const apply_end_date = filters.endDate
        const pro_level_code = filters.levelCode
        const g_budget_code = filters.budgetCode
        const staff_name = filters.staff
        const title_or_remark = filters.title_or_remark
        const status = filters.status
        const page_size = this.pageSize
        const page = this.page

        const apply_staff_id = this.isAdmin ? undefined : this.userId
        const res = await fetchProcurementApplications({
          apply_staff_id,
          fy_code,
          pro_no,
          apply_begin_date,
          apply_end_date,
          pro_level_code,
          g_budget_code,
          staff_name,
          title_or_remark,
          status,
          page_size,
          page,
        })
        this.tableData = res.data
        this.total = res.total
        console.log(res)
      } catch (e) {
        console.log(e)
      }
    },

    onSetting() {
      this.showDialog = true
    },
    filterClassFunction(row) {
      row.row.tx_num === 1 ? groupCount++ : ''
      if (groupCount % 2 === 0) {
        return 'table-stripe'
      }
    },
    onChangeBudgetGroup(budget) {
      console.log(budget)
      this.fetchData()
    },
    budgetDisabledMethod(item) {
      return false
    },
    async onAdd() {
      try {
        if (!(await this.checkLevel())) {
          return
        }
      } catch (e) {
        console.error(e)
      }
      this.$router.push({
        name: 'purchaseDailyProcurementAdd',
        params: {
          view: 'add',
          fyCode: this.preferences.filters.year,
        },
      })
    },
    onView(row) {
      this.$router.push({
        name: 'purchaseDailyProcurementView',
        // path: '/daily/procurement/view/' + row.pro_application_id,
        params: {
          view: 'view',
          id: row.pro_application_id,
        },
      })
    },
    onEdit(row) {
      if (!this.canEdit(row)) {
        return
      }
      this.$router.push({
        name: 'purchaseDailyProcurementEdit',
        // path: '/daily/procurement/edit/' + row.pro_application_id,
        params: {
          view: 'edit',
          id: row.pro_application_id,
        },
      })
    },
    onDelete(row) {
      console.log(row, this.canDelete(row))
      if (!this.canDelete(row)) {
        return
      }
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${row.pro_title} ?`,
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const paper_set_id = row.paper_set_id
          return new Promise((resolve, reject) => {
            delete paper_set_id
              .then(res => {
                if (this.editUser && this.editUser.paper_set_id === paper_set_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    statusFormat(status) {
      const s = this.statusList.find(item => item.value === status)
      if (s) {
        return s.label
      } else {
        return status
      }
    },
    async onChangeYear() {
      try {
        await this.fetchBudgetList()
        await this.fetchData()
      } catch (e) {
        console.log(e)
      }
    },
    canEdit(item) {
      switch (item.status) {
        case 'A':
        case 'G':
        case 'GA':
        case 'I':
        case 'K':
        case 'M':
        case 'MI':
        case 'MA':
          // case 'O':
          return true
      }
      return false
    },
    canDelete(item) {
      return item.status === 'A'
    },
    async checkLevel() {
      try {
        const levels = await fetchProcurementLevels()
        if (levels.length > 0) {
          return true
        } else {
          this.$alert(this.t('levelsEmptyInput'), this.t('levelsEmpty'), {
            confirmButtonText: this.t('goto'),
            cancelButtonText: this.$t('button.cancel'),
            showCancelButton: true,
            callback: action => {
              switch (action) {
                case 'cancel':
                case 'close':
                  // this.onBack()
                  break
                case 'confirm':
                  this.$router.push({
                    name: 'purchaseMasterProcurementLevel',
                    params: {},
                  })
                  break
              }
            },
          })
          return false
        }
      } catch (e) {
        console.error(e)
      }
      return false
    },
  },
}
</script>

<style lang="scss" scoped>
.page-procurement-applications-list {
  height: 100%;
}
.mini-form {
  .input {
    width: 150px;
    &.input-select {
      width: 150px;
    }
    &.input-date {
      width: 130px;
    }
  }
}
.actions-icon {
  display: inline-block;
  vertical-align: bottom;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}
.page-header {
  .filters {
    display: inline-block;
  }
}
.content {
  .pagination {
    padding: 5px 10px;
    text-align: right;
  }
}

.operation_icon i {
  margin: 0 10px;
  cursor: pointer;
}
.disabled {
  cursor: not-allowed !important;
  color: #c0c4cc;
}
</style>
