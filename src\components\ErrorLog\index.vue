<template>
  <div v-if="errorLogs.length > 0">
    <el-badge
      :is-dot="true"
      style="line-height: 25px; margin-top: -5px"
      @click.native="dialogTableVisible = true"
    >
      <el-button style="padding: 8px 10px" size="small" type="danger">
        <svg-icon icon-class="bug" />
      </el-button>
    </el-badge>

    <el-dialog :visible.sync="dialogTableVisible" :title="$t('errorLog.title')" width="80%">
      <el-table :data="errorLogs" border>
        <el-table-column :label="$t('errorLog.message')">
          <template v-if="scope && scope.row" slot-scope="scope">
            <div>
              <span class="message-title">{{ $t('errorLog.msg') }}:</span>
              <el-tag type="danger">
                {{ scope.row.err.message }}
              </el-tag>
            </div>
            <br>
            <div>
              <span class="message-title" style="padding-right: 10px">{{ $t('errorLog.info') }}: </span>
              <el-tag type="warning">
                {{ scope.row.vm.$vnode.tag }} {{ $t('errorLog.errorIn') }} {{ scope.row.info }}
              </el-tag>
            </div>
            <br>
            <div>
              <span class="message-title" style="padding-right: 16px">{{ $t('errorLog.url') }}: </span>
              <el-tag type="success">
                {{ scope.row.url }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('errorLog.stack')">
          <template v-if="scope && scope.row" slot-scope="scope">
            {{ scope.row.err.stack }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ErrorLog',
  data() {
    return {
      dialogTableVisible: false,
    }
  },
  computed: {
    errorLogs() {
      return this.$store.getters.errorLogs
    },
  },
}
</script>

<style scoped>
.message-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  padding-right: 8px;
}
</style>
