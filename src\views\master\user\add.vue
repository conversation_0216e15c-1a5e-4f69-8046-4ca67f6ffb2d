<template>
  <div class="userInfo">
    <el-form ref="userForm" :model="userForm" label-position="right" label-width="100px">
      <!-- 中文名稱 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('master.user.label.name_cn')"
        prop="name_cn"
      >
        <el-input v-model="userForm.name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('master.user.label.name_en')"
        prop="name_en"
      >
        <el-input v-model="userForm.name_en" clearable />
      </el-form-item>
      <!-- 登錄名稱 -->
      <el-form-item :label="$t('master.user.label.username')" prop="username">
        <el-input v-model="userForm.username" clearable />
      </el-form-item>
      <!-- 登錄密碼 -->
      <el-form-item
        :rules="[
          {
            required: userForm.username && userForm.username.length,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('master.user.label.password')"
        prop="password"
      >
        <el-input v-model="userForm.password" :disabled="disPwd" clearable type="password">
          <el-button
            v-if="disPwd"
            slot="append"
            type="mini"
            icon="el-icon-close"
            @click="clearPassword"
          />
        </el-input>
      </el-form-item>
      <!-- 確認密碼 -->
      <el-form-item
        :rules="[
          {
            required: userForm.username && userForm.username.length,
            trigger: 'blur',
            validator: valiadatePass,
          },
        ]"
        :label="$t('master.user.label.confirm')"
        prop="confirmPassword"
      >
        <el-input v-model="userForm.confirmPassword" :disabled="disPwd" clearable type="password" />
      </el-form-item>
      <!-- 類別 -->
      <el-form-item :label="$t('master.user.label.type')" prop="user_type">
        <el-radio-group v-model="userForm.user_type">
          <el-radio :label="$t('master.user.value.typeAdmin')">
            {{ $t('master.user.label.type_admin') }}
          </el-radio>
          <el-radio :label="$t('master.user.value.typeGeneral')">
            {{ $t('master.user.label.type_general') }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 角色 -->
      <el-form-item :label="$t('master.user.label.role')" prop="role_id">
        <el-select v-model="userForm.role_id" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in roles"
            :key="item.role_id"
            :label="item.role_name_cn"
            :value="item.role_id"
          />
        </el-select>
      </el-form-item>
      <!-- 組別 -->
      <el-form-item :label="$t('master.user.label.group')" prop="group">
        <el-input v-model="userForm.group" clearable />
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editUser ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createUser, editUser } from '@/api/master/users'
import { fetchRoles } from '@/api/master/role'
const virtualPassword = 'this.virtual.password'
export default {
  name: 'MasterUserAdd',
  props: {
    editUser: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      system: 'AC',
      userForm: {
        user_id: 1,
        user_code: '',
        username: 'system',
        name_cn: this.$t('login.systemAdmin'),
        name_en: 'System Admin',
        user_type: 'G',
        role_id: '',
        password: '',
        confirmPassword: '',
        group: '',
      },
      defaultForm: {
        user_id: '',
        user_code: '',
        username: '',
        name_cn: '',
        name_en: '',
        user_type: 'A',
        role_id: '',
        password: '',
        confirmPassword: '',
        group: '',
      },
      roles: [],
      disPwd: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    editUser() {
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    checkRequired(rule, value, callback) {},
    valiadatePass(rule, value, callback) {
      if (this.userForm.password !== this.userForm.confirmPassword) {
        // return callback(new Error('两次输入密码不一致!'))
        return callback(new Error(' '))
      } else {
        callback()
      }
    },
    clearPassword() {
      this.userForm.password = ''
      this.userForm.confirmPassword = ''
      this.disPwd = false
    },
    initData() {
      this.disPwd = false
      if (this.editUser) {
        // 編輯
        this.userForm = Object.assign({}, this.editUser)
        if (this.userForm.username && this.userForm.username.length > 0) {
          this.disPwd = true
          this.userForm.password = this.userForm.confirmPassword = virtualPassword
        }
      } else {
        // 新增
        this.userForm = Object.assign({}, this.defaultForm)
      }
      this.userForm.role_id = this.userForm.role_id ? parseInt(this.userForm.role_id) : ''

      fetchRoles(this.system)
        .then(res => {
          this.roles = res
          if (res.length > 0) {
            const role = res.find(i => i.role_id === this.userForm.role_id)
            if (!role) {
              this.userForm.role_id = ''
            }
          }
        })
        .catch(() => {})
    },
    onSave() {
      this.$refs['userForm'].validate(valid => {
        if (!valid) {
          return false
        }
        const user_id = this.userForm.user_id
        const user_code = this.userForm.user_code ? this.userForm.user_code : null
        const username = this.userForm.username
        const name_cn = this.userForm.name_cn
        const name_en = this.userForm.name_en
        const user_type = this.userForm.user_type
        const role_id = this.userForm.role_id
        const password =
          this.userForm.password === virtualPassword ? undefined : this.$md5(this.userForm.password)
        // const group = this.userForm.group // 群组

        if (this.editUser) {
          // 編輯
          editUser(user_id, user_code, username, password, name_cn, name_en, user_type, role_id)
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createUser(user_code, username, password, name_cn, name_en, user_type, role_id)
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style scoped></style>
