<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <div>
      <VBreadCrumb class="breadcrumb" />
      <div class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 年份 -->
          <el-form-item :label="$t('filters.years')">
            <el-select
              ref="year"
              v-model="preferences.filters.selectedYearCode"
              class="year"
              style="width: 110px"
              @change="reloadData"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_code"
              />
            </el-select>
          </el-form-item>
          <!-- 賬目範圍 -->
          <el-form-item :label="$t('filters.fundRange')">
            <el-select
              v-model="preferences.filters.fund_id"
              class="fund"
              style="width: 120px"
              @change="onChangeFund"
            >
              <!--          @change="changeFund"-->
              <el-option :label="$t('filters.all')" value="" />
              <el-option
                v-for="item in fundList"
                :key="item.fund_id"
                :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
                :value="item.fund_id"
              />
            </el-select>
            <SelectTree
              v-model="preferences.filters.fund_ac_fund_id"
              :options="accountTree"
              :props="{
                label: language === 'en' ? 'name_en' : 'name_cn',
                value: 'fund_id',
                code: 'code',
                group_id: 'fund_id',
                value_id: 'account_id',
                children: 'children',
              }"
              :group-id.sync="preferences.filters.fund_ac_fund_id"
              :select-group.sync="preferences.filters.fund_selectGroup"
              :show-all-option="true"
              :all-option-text="$t('enquiry.general.label.allAccountType')"
              style="width: 300px"
              @change="onChangeAccount"
            />
          </el-form-item>

          <el-form-item>
            <el-button size="mini" type="primary" :loading="btnLoading1" @click="onPrintOpening">
              {{ $t('periodic.balanceTransfer.button.ob') }}
            </el-button>
            <el-button size="mini" type="primary" :loading="btnLoading2" @click="onPrintClosing">
              {{ $t('periodic.balanceTransfer.button.cs') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('ALL')"
          />
        </div>
      </div>
      <div class="balance-transfer-table">
        <ETable
          ref="table"
          v-loading="!loading && tableLoading"
          :data="tableData"
          :style-columns="styleColumns"
          :lang-key="langKey"
          :show-index="true"
          :show-actions="true"
          :actions-min-width="5"
          :show-checkbox="false"
          :default-top="230"
          :span-method="spanMethod"
          action-label=" "
          border
          @changeWidth="changeColumnWidth"
        >
          <template slot="columns">
            >
            <el-table-column :label="$t(langKey + 'lastData')">
              <!--  <el-table-column-->
              <!--  v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"-->
              <!--  :key="item.ss_key"-->
              <!--  :label="$t(langKey + item.ss_key)"-->
              <!--  :align="item.alignment"-->
              <!--  :class-name="item.ss_key + ' mini-form'"-->
              <!--  :width="item.width"-->
              <!--  :property="$refs.table.column_property(item)"-->
              <!--  :column-key="item.ss_key"-->
              <!--&gt;-->
              <!--  <template v-if="scope && scope.row" slot-scope="scope">-->
              <!--    <span-->
              <!--      v-if="item.ss_key === 'adj_amount'"-->
              <!--      class="edit-row"-->
              <!--    >-->
              <!--      <ENumeric-->
              <!--        v-if="scope.row.last.ac_B === 'Y'"-->
              <!--        v-model="scope.row.last.adj_amount"-->
              <!--        :disable-update="loading"-->
              <!--        :class="scope.row.last.adj_amount === scope.row.last.ob_amount ? '' : 'different'"-->
              <!--      />-->
              <!--    </span>-->
              <!--    <span v-else class="show-row">{{ formatCell(scope.row.this, $refs.table.column_property(item)) }}</span>-->
              <!--  </template>-->
              <!--</el-table-column>-->
              <!-- 編號 -->
              <el-table-column
                v-if="col_ac_code"
                :key="col_ac_code.ss_key"
                :label="$t(langKey + col_ac_code.ss_key)"
                :align="col_ac_code.alignment"
                :class-name="col_ac_code.ss_key + ' mini-form'"
                :width="col_ac_code.width"
                :column-key="col_ac_code.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ scope.row.last.ac_code }}</span>
                </template>
              </el-table-column>
              <!-- 賬目名稱 -->
              <el-table-column
                v-if="col_ac_name_"
                :key="col_ac_name_.ss_key"
                :label="$t(langKey + col_ac_name_.ss_key)"
                :align="col_ac_name_.alignment"
                :class-name="col_ac_name_.ss_key + ' mini-form'"
                :width="col_ac_name_.width"
                :column-key="col_ac_name_.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ scope.row.last[isEnglish ? 'ac_name_en' : 'ac_name_cn'] }}</span>
                </template>
              </el-table-column>
              <!-- 賬目簡稱 -->
              <el-table-column
                v-if="col_ac_abbr_"
                :key="col_ac_abbr_.ss_key"
                :label="$t(langKey + col_ac_abbr_.ss_key)"
                :align="col_ac_abbr_.alignment"
                :class-name="col_ac_abbr_.ss_key + ' mini-form'"
                :width="col_ac_abbr_.width"
                :column-key="col_ac_abbr_.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ scope.row.last[isEnglish ? 'ac_abbr_en' : 'ac_abbr_cn'] }}</span>
                </template>
              </el-table-column>
              <!-- 轉賬 -->
              <el-table-column
                v-if="col_ac_B"
                :key="col_ac_B.ss_key"
                :label="$t(langKey + col_ac_B.ss_key)"
                :align="col_ac_B.alignment"
                :class-name="col_ac_B.ss_key + ' mini-form'"
                :width="col_ac_B.width"
                :column-key="col_ac_B.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ scope.row.last['ac_B'] }}</span>
                </template>
              </el-table-column>
              <!-- 上年結餘 -->
              <el-table-column
                v-if="col_bf_amount"
                :key="col_bf_amount.ss_key"
                :label="$t(langKey + col_bf_amount.ss_key)"
                :align="col_bf_amount.alignment"
                :class-name="col_bf_amount.ss_key + ' mini-form'"
                :width="col_bf_amount.width"
                :column-key="col_bf_amount.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{
                    formatCell(scope.row.last, $refs.table.column_property(col_bf_amount))
                  }}</span>
                  <span>{{ getAmountType(scope.row.last.bf_amount) }}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column :label="$t(langKey + 'thisData')">
              <!-- 編號 -->
              <el-table-column
                v-if="col_ac_code"
                :key="col_ac_code.ss_key"
                :label="$t(langKey + col_ac_code.ss_key)"
                :align="col_ac_code.alignment"
                :class-name="col_ac_code.ss_key + ' this-col mini-form'"
                :width="col_ac_code.width"
                :column-key="col_ac_code.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ scope.row.last.ac_code }}</span>
                </template>
              </el-table-column>
              <!-- 賬目名稱 -->
              <el-table-column
                v-if="col_ac_name_"
                :key="col_ac_name_.ss_key"
                :label="$t(langKey + col_ac_name_.ss_key)"
                :align="col_ac_name_.alignment"
                :class-name="col_ac_name_.ss_key + ' this-col mini-form'"
                :width="col_ac_name_.width"
                :column-key="col_ac_name_.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ scope.row.last[isEnglish ? 'ac_name_en' : 'ac_name_cn'] }}</span>
                </template>
              </el-table-column>
              <!-- 賬目簡稱 -->
              <el-table-column
                v-if="col_ac_abbr_"
                :key="col_ac_abbr_.ss_key"
                :label="$t(langKey + col_ac_abbr_.ss_key)"
                :align="col_ac_abbr_.alignment"
                :class-name="col_ac_abbr_.ss_key + ' this-col mini-form'"
                :width="col_ac_abbr_.width"
                :column-key="col_ac_abbr_.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ scope.row.last[isEnglish ? 'ac_abbr_en' : 'ac_abbr_cn'] }}</span>
                </template>
              </el-table-column>
              <!-- 轉賬 -->
              <el-table-column
                v-if="col_ac_B"
                :key="col_ac_B.ss_key"
                :label="$t(langKey + col_ac_B.ss_key)"
                :align="col_ac_B.alignment"
                :class-name="col_ac_B.ss_key + ' this-col mini-form'"
                :width="col_ac_B.width"
                :column-key="col_ac_B.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ scope.row.last['ac_B'] }}</span>
                </template>
              </el-table-column>
              <!-- 預算期初 -->
              <el-table-column
                v-if="col_ob_amount"
                :key="col_ob_amount.ss_key"
                :label="$t(langKey + col_ob_amount.ss_key)"
                :align="col_ob_amount.alignment"
                :class-name="col_ob_amount.ss_key + ' this-col mini-form'"
                :width="col_ob_amount.width"
                :column-key="col_ob_amount.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{
                    formatCell(scope.row.this, $refs.table.column_property(col_ob_amount))
                  }}</span>
                  <span>{{ getAmountType(scope.row.this.ob_amount) }}</span>
                </template>
              </el-table-column>
              <!-- 期初調整 -->
              <el-table-column
                v-if="col_adj_amount"
                :key="col_adj_amount.ss_key"
                :label="$t(langKey + col_adj_amount.ss_key)"
                :align="col_adj_amount.alignment"
                :class-name="col_adj_amount.ss_key + ' this-col mini-form'"
                :width="col_adj_amount.width"
                :column-key="col_adj_amount.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span class="edit-row">
                    <ENumeric
                      v-if="scope.row.this.ac_B === 'Y'"
                      v-model="scope.row.this.adj_amount_temp"
                      :disable-update="loading"
                      :min="0"
                      :class="
                        checkNum(scope.row.this.adj_amount, scope.row.this.ob_amount)
                          ? ''
                          : 'different'
                      "
                      :style="{
                        width: 'calc(100% - 74px)',
                      }"
                      @change="onChangeAdj($event, scope.row)"
                    />
                    <el-select
                      v-model="scope.row.this.amountType"
                      style="width: 60px"
                      placeholder=" "
                      @change="onChangeAmountType($event, scope.row)"
                    >
                      <el-option value="dr" label="Dr" />
                      <el-option value="cr" label="Cr" />
                    </el-select>
                  </span>
                </template>
              </el-table-column>
              <!--<span-->
              <!--  v-if="item.ss_key === 'adj_amount'"-->
              <!--  class="edit-row"-->
              <!--&gt;-->
              <!--  <ENumeric-->
              <!--    v-if="scope.row.last.ac_B === 'Y'"-->
              <!--    v-model="scope.row.last.adj_amount"-->
              <!--    :disable-update="loading"-->
              <!--    :class="scope.row.last.adj_amount === scope.row.last.ob_amount ? '' : 'different'"-->
              <!--  />-->
              <!--</span>-->
              <!--<span v-else class="show-row">{{ formatCell(scope.row.this, $refs.table.column_property(item)) }}</span>-->
              <!--</template>-->
              <!--</el-table-column>-->
            </el-table-column>
          </template>
        </ETable>
      </div>
      <div class="total" :style="'width: ' + botWidth">
        <table style="margin: 0 0 0 auto">
          <tbody>
            <tr>
              <td rowspan="2">
                <el-button v-if="!isFirstYear" size="mini" type="primary" @click="onAutoTransfer">
                  {{ $t('periodic.balanceTransfer.button.autoTransfer') }}
                </el-button>
              </td>
              <td rowspan="2">
                <el-button size="mini" type="primary" @click="onEdit">
                  {{ $t('periodic.balanceTransfer.button.update') }}
                </el-button>
              </td>
              <td class="total-value">
                {{ $t('periodic.balanceTransfer.label.totalDr') }}(+)：
              </td>
              <td
                :class="{
                  unequal: !bfBalance(),
                }"
                class="total-value"
              >
                <span>{{ thisAmountFormat(total.total_bf_amount_dr) }}</span>
                <span>{{ getAmountType(total.total_bf_amount_dr) }}</span>
              </td>
              <!--<td-->
              <!--  :class="{-->
              <!--    'unequal': !obBalance()-->
              <!--  }"-->
              <!--  class="total-value">{{ thisAmountFormat(total.total_ob_amount_dr) }}</td>-->
              <td
                :class="{
                  unequal: !adjBalance(),
                }"
                class="total-value"
              >
                <!--{{ thisAmountFormat(total.total_adj_amount_dr) }}-->
                <span>{{ thisAmountFormat(total.total_adj_amount_dr) }}</span>
                <span>{{ getAmountType(total.total_adj_amount_dr) }}</span>
              </td>
            </tr>
            <tr>
              <td>{{ $t('periodic.balanceTransfer.label.totalCr') }}(-)：</td>
              <td
                :class="{
                  unequal: !bfBalance(),
                }"
              >
                <span>{{ thisAmountFormat(total.total_bf_amount_cr) }}</span>
                <span>{{ getAmountType(total.total_bf_amount_cr) }}</span>
              </td>
              <!--<td-->
              <!--  :class="{-->
              <!--    'unequal': !obBalance()-->
              <!--}">{{ thisAmountFormat(total.total_ob_amount_cr) }}</td>-->
              <td
                :class="{
                  unequal: !adjBalance(),
                }"
              >
                <span>{{ thisAmountFormat(total.total_adj_amount_cr) }}</span>
                <span>{{ getAmountType(total.total_adj_amount_cr) }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears } from '@/api/master/years'
import { getAccountTree } from '@/api/master/account'
import {
  editBalanceTransfer,
  fetchBalanceTransfer,
  fetchBalanceTransferV2,
} from '@/api/periodic/balanceTransfer'
import { fetchFunds } from '@/api/master/funds' // 撥款
import ETable from '@/components/ETable'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import ENumeric from '@/components/ENumeric'
import SelectTree from '@/components/SelectTree'
import handlePDF from './handlePDF'

import { amountFormat, toDecimal } from '@/utils'
import { getClosingBalance, getOpeningBalance } from '@/api/report'
import { exportExcel } from '@/utils/excel'
import { balanceTransferExport } from '@/api/report/excel'

export default {
  name: 'BalanceTransferIndex',
  components: {
    ETable,
    customStyle,
    ENumeric,
    SelectTree,
    VBreadCrumb,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      years: [],
      selectedYearId: '',
      tableData: [],
      total: {
        total_bf_amount_dr: 0,
        total_bf_amount_cr: 0,
        total_ob_amount_dr: 0,
        total_ob_amount_cr: 0,
        total_adj_amount_dr: 0,
        total_adj_amount_cr: 0,
      },
      data: [],
      dateList: [],
      monthList: [],
      langKey: 'periodic.balanceTransfer.label.',
      tableColumns: [
        'ac_code',
        'ac_name_',
        'ac_abbr_',
        'ac_B',
        'bf_amount',
        'ob_amount',
        'adj_amount',
      ],
      amountColumns: ['bf_amount', 'ob_amount', 'adj_amount'],
      preferences: {
        filters: {
          selectedYearCode: '',
          fund_id: '',
          fund_ac_code: '',
          fund_ac_fund_id: '',
          fund_selectGroup: '',
        },
      },
      childPreferences: ['fund_ac_fund_id'],
      showYearPicker: false,

      fundList: [],
      accountTree: [],
      btnLoading1: false,
      btnLoading2: false,
      isAutoTransferChange: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
    col_ac_code() {
      return this.getCol('ac_code')
    },
    col_ac_name_() {
      return this.getCol('ac_name_')
    },
    col_ac_abbr_() {
      return this.getCol('ac_abbr_')
    },
    col_ac_B() {
      return this.getCol('ac_B')
    },
    col_bf_amount() {
      return this.getCol('bf_amount')
    },
    col_ob_amount() {
      return this.getCol('ob_amount')
    },
    col_adj_amount() {
      return this.getCol('adj_amount')
    },
    botWidth() {
      if (this.styleColumns && this.styleColumns.length > 0) {
        const arr = this.styleColumns.filter(i => i.ss_key !== '_index')
        let width = 0
        for (let i = 0; i < arr.length; i++) {
          const item = arr[i]
          switch (item.ss_key) {
            case 'ac_code':
            case 'ac_name_':
            case 'ac_abbr_':
            case 'ac_B':
              width += item.width * 2
              break
            case 'bf_amount':
            case 'ob_amount':
            case 'adj_amount':
              width += item.width
              break
          }
        }
        return width + 'px'
      }
      return ''
    },
    isFirstYear() {
      return (
        this.years &&
        this.years.length > 0 &&
        this.preferences.filters.selectedYearCode === this.years[0].fy_code
      )
    },
  },
  watch: {
    tableData: {
      deep: true,
      handler(newVal, oldVal) {
        if (this.loading) {
          return
        }
        let dr = 0
        let cr = 0
        newVal.forEach(item => {
          if (item.itemIndex > 0) {
            return
          }
          const n = Number(item.this.adj_amount)
          if (typeof n === 'number') {
            if (!isNaN(n)) {
              if (n > 0) {
                dr += toDecimal(n)
              } else {
                cr += toDecimal(n)
              }
            }
          }
        })
        this.$set(this.total, 'total_adj_amount_dr', dr)
        this.$set(this.total, 'total_adj_amount_cr', cr)
        // this.total.total_adj_amount_dr = dr
        // this.total.total_adj_amount_cr = cr
      },
    },
  },
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  methods: {
    getCol(ss_key) {
      if (this.styleColumns) {
        const item = this.styleColumns.find(i => i.ss_key === ss_key)
        if (item) {
          return item
        }
      }
      return false
    },
    checkNum(a, b) {
      const aa = Number(a).toFixed(2)
      const bb = Number(b).toFixed(2)
      return aa - bb === 0
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.fundList = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (!this.preferences.filters.selectedYearCode) {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          } else {
            if (this.years && this.years.length > 0) {
              let bool = false
              this.years.forEach(i => {
                if (i.fy_code === this.preferences.filters.selectedYearCode) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedYearCode = this.years[0].fy_code
              }
            }
          }
          if (
            this.fundList &&
            this.fundList.length > 0 &&
            this.preferences.filters.fund_id !== ''
          ) {
            let bool = false
            this.fundList.forEach(i => {
              if (i.fund_id === this.preferences.filters.fund_id) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.fund_id = this.fundList[0].fund_id
            }
          }

          const fund_id = this.preferences.filters.fund_id
          if (fund_id) {
            this.loadAccountTree(this.preferences.filters.fund_id)
          }
          return Promise.resolve()
        })
        .then(this.updateChildPreference)
        .then(this.reloadData)
        .finally(() => {
          this.loading = false
        })
    },
    formatTableData(data) {
      // const newData = data.map(item => {
      //   const newItem = Object.assign({}, item)
      //   let bf_amount = Number(item.bf_amount)
      //   let ob_amount = Number(item.ob_amount)
      //   let adj_amount = Number(item.adj_amount)
      //
      //   if (isNaN(bf_amount)) {
      //     bf_amount = 0
      //   }
      //   if (isNaN(ob_amount)) {
      //     ob_amount = 0
      //   }
      //   if (isNaN(adj_amount)) {
      //     adj_amount = 0
      //   }
      //
      //   newItem.bf_amount = bf_amount
      //   newItem.ob_amount = ob_amount
      //   newItem.adj_amount = adj_amount
      //   return newItem
      // })
      const newData = []
      data.forEach(item => {
        const line = item.from_ledgers.length
        item.from_ledgers.forEach((row, index) => {
          const n = Number(item.adj_amount)
          if (n >= 0) {
            item.amountType = 'dr'
            item.adj_amount_temp = item.adj_amount
          } else {
            item.amountType = 'cr'
            item.adj_amount_temp = -item.adj_amount
          }
          newData.push({
            last: row,
            this: item,
            line,
            itemIndex: index,
          })
        })
      })
      return newData
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        const fy_code = this.preferences.filters.selectedYearCode
        const fund_id = this.preferences.filters.fund_ac_fund_id
          ? this.preferences.filters.fund_ac_fund_id
          : this.preferences.filters.fund_id
        this.loading = true
        fetchBalanceTransferV2({
          fy_code,
          fund_id,
        })
          .then(res => {
            this.tableData = this.formatTableData(res.ledgers)
            this.total = res.total
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    onEdit() {
      const fy_code = this.preferences.filters.selectedYearCode
      const postDate = this.tableData
        .filter(item => item.this.ac_B === 'Y' && item.itemIndex === 0)
        // .filter(item => item.adj_amount !== 0 && item.ob_amount !== 0) // 兩個都是0 不更新
        .map(item => {
          const account_id = item.this.account_id
          const adj_amount = item.this.adj_amount
          return {
            account_id,
            adj_amount,
          }
        })

      editBalanceTransfer({
        fy_code,
        adjust_json: postDate,
      })
        .then(res => {
          console.log(res)
          this.reloadData()
          this.$message.success(this.$t('message.success'))
        })
        .catch(err => {
          console.log(err)
          // this.$message.success(this.$t('message.success'))
        })
    },
    onChangeFund(fund_id) {
      console.log('change fund_id', fund_id)
      if (!fund_id) {
        this.accountTree = []
      } else {
        this.loadAccountTree(fund_id)
      }
      this.preferences.filters.fund_ac_fund_id = ''
      this.preferences.filters.ac_code = ''
      this.preferences.filters.fund_selectGroup = false
      this.reloadData()
    },
    formatAccountTree(array) {
      for (let i = 0; i < array.length; i++) {
        if (array[i].account_id) {
          delete array[i]
        } else {
          if (array[i].children) {
            if (array[i].children.length) {
              array[i].children = this.formatAccountTree(array[i].children)
            } else {
              delete array[i].children
            }
          }
        }
      }
      // for (let i = array.length - 1; i > 0; i--) {
      //   if (array[i] == null) {
      //     array = array.splice(i, 1)
      //   }
      // }
      return array
    },
    // 會計科目樹
    loadAccountTree(fund_id) {
      return new Promise((resolve, reject) => {
        getAccountTree(fund_id)
          .then(res => {
            this.accountTree = JSON.parse(JSON.stringify(this.formatAccountTree(res)))

            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    onChangeAccount(val) {
      console.log(val)
      this.reloadData()
    },
    onAutoTransfer() {
      const fy_code = this.preferences.filters.selectedYearCode
      const index = this.years.findIndex(i => i.fy_code === fy_code)
      if (index === 0) {
        return
      } else if (index === -1) {
        return
      }
      const checkHasChange = this.tableData.some(item => {
        if (item.this.ac_B === 'Y') {
          const ob_amount = Number(item.this.ob_amount)
          const adj_amount_temp = Number(item.this.adj_amount_temp)
          const amountType = item.this.amountType
          let newAmount
          let newAmountType
          if (ob_amount >= 0) {
            newAmount = ob_amount
            newAmountType = 'dr'
          } else {
            newAmount = -ob_amount
            newAmountType = 'cr'
          }
          if (newAmount !== adj_amount_temp || newAmountType !== amountType) {
            return true
          }
        }
        return false
      })
      if (checkHasChange) {
        this.$confirm(
          this.$t('periodic.balanceTransfer.message.autoTransfer'),
          this.$t('confirm.warningTitle'),
          {
            confirmButtonText: this.$t('confirm.confirmButtonText'),
            cancelButtonText: this.$t('confirm.cancelButtonText'),
            type: 'warning',
          },
        )
          .then(() => {
            this.autoTransfer()
          })
          .catch(() => {})
      }
    },
    // 使用前，需要先檢查數據
    autoTransfer() {
      this.tableData = this.tableData.map(item => {
        const newItem = Object.assign({}, item)
        if (newItem.this.ac_B === 'Y') {
          newItem.this.adj_amount = newItem.this.ob_amount
          const n = Number(item.this.adj_amount)
          const cacheType = item.this.amountType
          const cacheAmount = item.this.adj_amount_temp
          if (n >= 0) {
            item.this.amountType = 'dr'
            item.this.adj_amount_temp = item.this.adj_amount
          } else {
            item.this.amountType = 'cr'
            item.this.adj_amount_temp = -item.this.adj_amount
          }
          if (cacheType !== item.this.amountType || cacheAmount !== item.this.adj_amount_temp) {
            this.isAutoTransferChange = true
          }
        }
        return newItem
      })
    },
    formatCell(row, key) {
      if (this.amountColumns.includes(key)) {
        return amountFormat(Math.abs(row[key]))
      }
      return row[key]
    },
    thisAmountFormat(val) {
      return amountFormat(Math.abs(val))
    },
    getCurrentAccount() {
      const fund_id = this.preferences.filters.fund_id
      const fund_ac_fund_id = this.preferences.filters.fund_ac_fund_id
      const getTreeNode = (data, fid) => {
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (!item) continue
          if (item && item.fund_id && item.fund_id === fid) {
            return item
          }
          if (item.children) {
            const v = getTreeNode(item.children, fid)
            if (v) {
              return v
            }
          }
        }
      }
      let node
      let account
      if (fund_ac_fund_id) {
        node = getTreeNode(this.accountTree, fund_ac_fund_id)
        if (node) {
          account = {
            name_cn: node.name_cn,
            name_en: node.name_en,
            fund_id: fund_ac_fund_id,
          }
        }
      } else {
        node = this.fundList.find(i => i.fund_id === fund_id)
        if (node) {
          account = {
            name_cn: node.fund_name_cn,
            name_en: node.fund_name_en,
            fund_id: fund_id,
          }
        }
      }
      if (!account) {
        account = {
          name_cn: this.$t('daily.label.all'),
          name_en: this.$t('daily.label.all', 'en'),
          fund_id: 0,
        }
      }
      return account
    },
    onPrintOpening() {
      this.btnLoading1 = true
      const fy_code = this.preferences.filters.selectedYearCode
      const fund_id = this.preferences.filters.fund_id
      const year = this.years.find(i => i.fy_code === fy_code)
      const fund = this.fundList.find(i => i.fund_id === fund_id)
      const account = this.getCurrentAccount()
      if (fy_code && year) {
        this.loading = true
        const title = 'openingBalance'
        getOpeningBalance(fy_code)
          .then(res =>
            this.onPrint(res, year, fund, account, title).finally(() => {
              this.btnLoading1 = false
            }),
          )
          .finally(() => {
            this.loading = false
          })
      }
    },
    onPrintClosing() {
      this.btnLoading2 = true
      const fy_code = this.preferences.filters.selectedYearCode
      const fund_id = this.preferences.filters.fund_id
      const year = this.years.find(i => i.fy_code === fy_code)
      const fund = this.fundList.find(i => i.fund_id === fund_id)
      const account = this.getCurrentAccount()
      if (fy_code && year) {
        this.loading = true
        const title = 'closingBalance'
        getClosingBalance(fy_code)
          .then(res =>
            this.onPrint(res, year, fund, account, title).finally(() => {
              this.btnLoading2 = false
            }),
          )
          .finally(() => {
            this.loading = false
          })
      }
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id
      const fund_id = this.preferences.filters.fund_id || undefined
      const fy_code = this.preferences.filters.selectedYearCode
      if (!user_id || !fy_code) {
        // this.$message.error('')
        return
      }
      this.loading = true
      balanceTransferExport({ user_id, export_type, fy_code, fund_id })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log(row, column, rowIndex, columnIndex)
      if (column && column.className && column.className.includes('this-col')) {
        if (row.itemIndex === 0) {
          return {
            rowspan: row.line,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1,
        }
      }
    },
    bfBalance() {
      const dr = toDecimal(Number(Number(this.total.total_bf_amount_dr).toFixed(2)))
      const cr = toDecimal(Number(Number(this.total.total_bf_amount_cr).toFixed(2)))
      return toDecimal(dr + cr) === 0
    },
    obBalance() {
      const dr = toDecimal(Number(Number(this.total.total_ob_amount_dr).toFixed(2)))
      const cr = toDecimal(Number(Number(this.total.total_ob_amount_cr).toFixed(2)))
      return toDecimal(dr + cr) === 0
    },
    adjBalance() {
      const dr = toDecimal(Number(Number(this.total.total_adj_amount_dr).toFixed(2)))
      const cr = toDecimal(Number(Number(this.total.total_adj_amount_cr).toFixed(2)))
      return toDecimal(dr + cr) === 0
    },
    getAmountType(amount) {
      console.log(Number(amount))
      return Number(amount) >= 0 ? 'Dr' : 'Cr'
    },
    onChangeAdj(val, row) {
      console.log(val, row)
      const n = Number(val)
      row.this.adj_amount = row.this.amountType === 'dr' ? n : -n
    },
    onChangeAmountType(val, row) {
      const n = Number(row.this.adj_amount_temp)
      row.this.adj_amount = row.this.amountType === 'dr' ? n : -n
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
      }
      /*line-height: 30px;*/
      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        height: 27px !important;
        line-height: 27px !important;
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
      .vc_chq_date {
        margin: 2px 0;
        .cell {
          height: 27px !important;
          line-height: 27px !important;
          padding: 0;
          .el-select {
            width: 100%;
          }
        }
      }
      .chq_no_cell {
        cursor: pointer;
        &.void {
          color: #b30000;
        }
      }
    }
  }
  .balance-transfer-table {
    height: calc(100vh - 250px);

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td:nth-last-child(2) {
          margin: 2px 0;
          .cell {
            /*height: 27px;*/
            /*line-height: 27px;*/
            height: 27px !important;
            line-height: 27px !important;
            .el-select {
              width: 100%;
            }
            span.show-row {
              padding: 0 10px;
            }
            .different {
              color: #ff0000;
            }
          }
          &.this-col {
            .cell {
              padding: 0;
            }
          }
        }
        td:nth-last-child(1) {
          border: none;
          background: #ffffff;
          .cell {
            background: #ffffff;
          }
        }
      }
    }
  }
  .total {
    max-width: 100%;
    margin: 5px 0;
    /deep/ {
      .el-form-item.el-form-item--medium {
        margin-right: 0;
      }
      .el-input {
        width: 100px;
        input {
          text-align: right;
        }
      }

      span {
        line-height: 25px !important;
        height: 25px !important;
      }
      .el-button {
        padding: 0px 15px;
        vertical-align: middle;
      }
      td {
        text-align: right;
        width: 100px;
        height: 30px;
      }
      td.total-value {
        width: 130px;
      }
      .unequal {
        color: red;
      }
    }
  }
}
/deep/ {
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: #ffffff;
  }

  .table-stripe th,
  .table-stripe td {
    background: #ffffff;
  }

  .this-col.mini-form {
    vertical-align: baseline;
  }

  .this-col .cell {
    display: block;
    position: absolute;
    width: 100%;
    height: initial;
    padding: 0.5px 10px;
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td > .cell {
    background-color: #f5f7fa;
  }
}
.edit-row {
  margin: 0;
  padding: 0;
  height: 25px;
  line-height: 25px;
  display: block;
}
</style>
