<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <!-- 會計週期 -->
        <el-select
          v-model="preferences.filters.year"
          class="year"
          style="width: 110px"
          @change="fetchTree"
        >
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>

        <svg-icon v-if="hasPermission_Add" icon-class="new_folder" @click="onAddGroup" />
        <div v-for="i in staffLevel" :key="i" class="selectLevel" @click="expandLevel(i)">
          {{ i }}
        </div>
        <div class="selectLevel" @click="enableYear">
          {{ preferences.filters.isActive ? $t('common.no') : $t('common.yes') }}
        </div>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 按鈕 -->
          <div
            v-if="hasPermission_Input"
            :title="$t('btnTitle.importExcel')"
            :disabled="!canImport"
            class="icon import"
            @click="onShowImport"
          >
            <svg-icon icon-class="import" class="action-icon" />
          </div>
          <!--  -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcel')"
            :disabled="!canImport"
            class="icon export"
            @click="onExport"
          >
            <svg-icon icon-class="export" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <tree-table
          ref="TreeTable"
          :data="staffTree"
          :eval-func="func"
          :eval-args="args"
          :expand-all="true"
          :first-field="language === 'en' ? 'name_en' : 'name_cn'"
          :first-field-align="firstFieldAlign"
          :first-field-width="firstFieldWidth"
          number-field="budget_code"
          folder-field="st_type_id"
          border
          @changeWidth="changeColumnWidth"
          @changeExpanded="onChangeExpanded"
        >
          <!--    編號姓名      -->
          <template v-if="scope && scope.row" slot="firstField" slot-scope="{ scope }">
            <i
              :class="{
                activate: scope.row.budget_active === 'Y',
                'edac-icon-file': scope.row.budget_type === 'D',
                'edac-icon-folder-f': scope.row.budget_type === 'F',
                'edac-icon-folder-c': scope.row.budget_type === 'C',
                'edac-icon-folder-g': scope.row.budget_type === 'G',
              }"
              class="edac-icon"
            />
            <span
              v-if="!scope.row.st_type_id"
              :class="{
                selected: scope.row.budget_active === 'Y',
              }"
              class="number-field"
            >{{ scope.row.budget_code }}</span>
            <span class="key">{{ scope.row[language === 'en' ? 'name_en' : 'name_cn'] }}</span>
          </template>

          <el-table-column
            v-for="item in filteredStyleColumns"
            :key="item.ss_id"
            :label="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :width="item.width"
            :property="item.ss_key"
            :column-key="item.ss_key"
            :formatter="formatter"
          />
          <!--操作列-->
          <el-table-column
            :label="$t('table.action')"
            align="left"
            header-align="left"
            min-width="180"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <div
                v-if="
                  scope.row.budget_type === 'F' ||
                    scope.row.budget_type === 'G' ||
                    scope.row.budget_type === 'C'
                "
                class="operation_icon"
              >
                <svg-icon
                  v-if="hasPermission_Add"
                  icon-class="new_folder"
                  @click="onAddGroup(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <svg-icon
                  v-if="hasPermission_Add && scope.row.budget_type !== 'F'"
                  icon-class="new_file"
                  @click="onAdd(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditGroup(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i
                  v-if="
                    hasPermission_Delete && (!scope.row.children || scope.row.children.length === 0)
                  "
                  class="el-icon-close"
                  @click="onDeleteType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
              </div>
              <div v-else class="operation_icon">
                <svg-icon class="no-cursor" icon-class="" />
                <svg-icon class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
                <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDeleteType(scope)" />
              </div>
            </template>
          </el-table-column>
        </tree-table>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="detailViews.includes(leftView)"
        :edit-object="editObject"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        :table-data="staffTree"
        @onCancel="onViewCancel"
      />
      <addGroup
        v-else-if="folderViews.includes(leftView)"
        :edit-object="editObject"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        :table-data="staffTree"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      table-type="tree"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      class="upload-dialog"
      width="450px"
    >
      <UploadExcel :on-success="onImport" :on-template="onExport" />
    </el-dialog>
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import addPage from './add'
import addGroup from './addGroup'
import { mapGetters } from 'vuex'
import treeToArray from '@/components/TreeTable/eval.js'
import treeTable from '@/components/TreeTable'
import UploadExcel from '@/components/UploadExcel/index'
import { getBudgetYears, fetchYears } from '@/api/master/years'
// 樣式
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
// 權限
import mixinPermission from '@/views/mixins/permission'
// 偏好
import loadPreferences from '@/views/mixins/loadPreferences'

// 導出Excel
import { exportExcel, importExcel } from '@/utils/excel'
import { deleteBudget, exportBudgets, fetchBudgetTree, importBudgets } from '@/api/budget'
import { amountFormat } from '@/utils'

export default {
  name: 'BudgetSettingIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    addGroup,
    treeTable,
    UploadExcel,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      leftView: '',
      funds: [],
      editObject: null,
      editTypeObject: null,
      func: treeToArray,
      expandAll: false,
      editParent: null,
      args: [null, null, 'timeLine'],
      staffTree: [],
      years: [],
      selectedYear: '',
      staffLevel: 0,
      currentLevel: 99,
      isActive: false,
      importDialog: false,
      loading: false,

      langKey: 'budget.budgetSet.label.',
      tableColumns: ['manager_name_', 'member_name_', 'amount'],

      folderViews: ['addBudgetGroup', 'editBudgetGroup', 'addBudgetItem', 'editBudgetItem'],
      detailViews: ['addBudgetDetail', 'editBudgetDetail'],

      preferences: {
        filters: {
          year: '',
          currentLevel: 99,
          isActive: false,
          expandedList: '',
        },
      },
      exportFileName: 'staffs', // 導出文件名
    }
  },
  computed: {
    ...mapGetters(['language']),
    canImport() {
      return !!this.preferences.filters.year
    },
    filteredStyleColumns() {
      return this.styleColumns.filter(item => item.ss_key !== 'first_field')
    },
  },
  created() {},
  mounted() {
    this.fetchData()
  },
  methods: {
    expandLevel(level) {
      if (level) {
        this.$refs.TreeTable.showLevel(level)
        this.preferences.filters.currentLevel = level
        this.preferences.filters.expandedList = ''
      } else {
        this.$refs.TreeTable.setExpandItem(this.preferences.filters.expandedList)
      }
    },
    handleMaxLevel() {
      this.$nextTick(() => {
        this.staffLevel = this.$refs.TreeTable.getMaxLevel()
      })
    },
    enableYear() {
      this.preferences.filters.isActive = !this.preferences.filters.isActive
      this.fetchTree()
    },
    onAdd(scope) {
      this.editObject = null
      this.editTypeObject = null
      this.editParent = scope && scope.row
      this.leftView = 'addBudgetDetail'
    },
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'editBudgetDetail'
      this.editParent = scope.row.parent
    },
    onAddGroup(scope) {
      this.editObject = null
      this.editTypeObject = null
      this.editParent = scope && scope.row
      if (scope.row && scope.row.budget_type === 'C') {
        this.leftView = 'addBudgetItem'
      } else {
        this.leftView = 'addBudgetGroup'
      }
    },
    onEditGroup(scope) {
      this.editObject = scope.row
      this.editTypeObject = null
      this.editParent = scope.row.parent
      if (scope.row && scope.row.budget_type === 'C') {
        this.leftView = 'editBudgetItem'
      } else {
        this.leftView = 'editBudgetGroup'
      }
    },
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const budget_id = scope.row.budget_id
          return new Promise((resolve, reject) => {
            deleteBudget(budget_id)
              .then(res => {
                if (this.editObject && this.editObject.budget_id === budget_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    onDeleteType(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const budget_id = scope.row.budget_id
          const fy_code = this.preferences.filters.year
          return new Promise((resolve, reject) => {
            deleteBudget({ fy_code, budget_id })
              .then(res => {
                if (this.editTypeObject && this.editTypeObject.budget_id === budget_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      const api = this.$route.name === 'assistanceBudget' ? fetchYears : getBudgetYears
      api()
        .then(res => {
          this.years = res
        })
        // 加載偏好設置
        .then(this.loadUserPreference)
        .then(() => {
          if (!this.preferences.filters.year) {
            this.preferences.filters.year =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          } else {
            if (this.years && this.years.length > 0) {
              let bool = false
              this.years.forEach(i => {
                if (i.fy_code === this.preferences.filters.year) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.year = this.years[0].fy_code
              }
            }
          }
        })
        .then(() => {
          const fy_code = this.preferences.filters.year
          return fetchBudgetTree({ fy_code })
        })
        .then(res => {
          this.staffTree = res
          this.handleMaxLevel()
          this.$nextTick(() => {
            this.expandLevel(this.preferences.filters.currentLevel)
          })
        })
        .catch(() => {})
    },
    fetchTree() {
      this.leftView = null
      this.editObject = null
      this.editParent = null
      this.editTypeObject = null
      const fy_code = this.preferences.filters.year
      const budget_active = this.preferences.filters.isActive ? 'Y' : undefined
      return new Promise((resolve, reject) => {
        fetchBudgetTree({ fy_code, budget_active })
          .then(res => {
            this.staffTree = res
            this.handleMaxLevel()
            this.$nextTick(() => {
              this.expandLevel(this.preferences.filters.currentLevel)
              resolve()
            })
          })
          .catch(err => reject(err))
      })
    },
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.fetchTree()
      }
    },
    onExport() {
      if (!this.preferences.filters.year) {
        return
      }
      if (this.loading) {
        return
      }
      this.loading = true
      const fy_code = this.preferences.filters.year
      exportBudgets(fy_code)
        .then(res => exportExcel(res, this.exportFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onShowImport() {
      if (this.preferences.filters.year) {
        this.importDialog = true
      }
    },
    onImport({ results, header }) {
      this.loading = true
      const fy_code = this.preferences.filters.year
      importExcel(this, importBudgets, results, header, { fy_code })
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
    onChangeExpanded(listStr) {
      this.preferences.filters.expandedList = listStr
      this.preferences.filters.currentLevel = 0
    },
    formatter(row, column) {
      let key = column.columnKey
      if (key && key[key.length - 1] === '_') {
        key += this.language === 'en' ? 'en' : 'cn'
      }
      if (key === 'amount') {
        return amountFormat(row.budget_amount)
      }
      return row[key]
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .year {
    width: 100px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}
.icon {
  cursor: pointer;
  &[disabled] {
    .svg-icon {
      color: #b9b6b6;
      cursor: not-allowed;
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.tree-table {
  .operation_icon {
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
</style>
