<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <div>
      <header v-if="false">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ $t('router.settingScreenBasicSetting') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t($route.meta.title) }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </header>
      <div class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 類型 -->
          <el-form-item :label="$t('filters.type')">
            <el-select
              v-model="preferences.filters.selectedType"
              class="year"
              style="width: 180px"
              @change="onChangeType"
            >
              <el-option
                v-for="(key, value) in typeOption"
                :key="key"
                :label="key"
                :value="value"
              />
            </el-select>
          </el-form-item>
          <!-- 銀行 -->
          <el-form-item :label="$t('filters.bank')">
            <el-select
              v-model="preferences.filters.selectedAcCode"
              class="bank"
              style="width: 250px"
              @change="changeBank"
            >
              <el-option
                v-for="item in bankList"
                :key="item.ac_code"
                :label="language === 'en' ? item.ac_name_en : item.ac_name_cn"
                :value="item.ac_code"
              />
            </el-select>
          </el-form-item>

          <!-- 支票簿 -->
          <el-form-item
            v-if="preferences.filters.selectedType === 'C'"
            :label="$t('filters.cheque_book')"
          >
            <el-select
              v-model="preferences.filters.selectedChequeCode"
              class="cheque"
              style="width: 150px"
              @change="reloadData"
            >
              <el-option
                v-for="item in chequeBooks"
                :key="item.cheque_book_id"
                :label="item.chqbk_code"
                :value="item.chqbk_code"
              />
            </el-select>
          </el-form-item>

          <!-- 年份 -->
          <el-form-item
            v-if="preferences.filters.selectedType !== 'C'"
            :label="$t('filters.years')"
          >
            <el-select
              v-if="preferences.filters.selectedType !== 'C'"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 110px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
          </el-form-item>

          <!-- 月份 -->
          <el-form-item
            v-if="preferences.filters.selectedType !== 'C'"
            :label="$t('filters.months')"
          >
            <el-select
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
              @change="reloadData"
            >
              <el-option :label="allMonth.label" :value="allMonth.value" />
              <el-option
                v-for="item in monthList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="actions-icon mini-form">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
          <i
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('ALL')"
          />
          <el-button
            :loading="printing"
            style="margin-left: 10px"
            size="mini"
            type="primary"
            @click="onPrint(false)"
          >
            {{ $t('button.chequePrint') }}
          </el-button>
        </div>
      </div>
      <div class="cheque-table">
        <ETable
          ref="table"
          v-loading="!loading && tableLoading"
          :data="tableData"
          :style-columns="styleColumns"
          :lang-key="langKey"
          :show-index="false"
          :show-actions="true"
          :show-checkbox="true"
          :default-top="230"
          :selectable="selectable"
          heigth="100%"
          action-label=" "
          border
          @changeWidth="changeColumnWidth"
          @selection-change="handleSelectionChange"
        >
          <template slot="columns">
            <el-table-column
              v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"
              :key="item.ss_key"
              :label="$t(langKey + item.ss_key)"
              :align="item.alignment"
              :class-name="item.ss_key + ' mini-form'"
              :width="item.width"
              :property="$refs.table.column_property(item)"
              :column-key="item.ss_key"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="scope.row.void_cheque_id && item.ss_key === 'vc_chq_date'">
                  <el-select v-model="scope.row.pd_code" @change="handleVoid(scope)">
                    <el-option :label="$t('common.notApplicable')" value="" />
                    <el-option
                      v-for="item in periods"
                      :key="item.pd_id"
                      :label="item.pd_name"
                      :value="item.pd_code"
                    />
                  </el-select>
                </span>
                <span
                  v-else-if="item.ss_key === 'chq_no' && preferences.filters.selectedType === 'C'"
                  :class="'chq_no_cell' + (scope.row.void_cheque_id ? ' void' : '')"
                  @click="onEdit(scope)"
                >{{ scope.row.chq_no }}</span>
                <span v-else>{{
                  $refs.table.customFormatter(
                    item.ss_key,
                    scope.row[$refs.table.column_property(item)]
                  )
                }}</span>
              </template>
            </el-table-column>
          </template>
          <!--        <template v-if="preferences.filters.selectedType === 'C'" slot-scope="{ scope }" slot="actions">-->
          <!--          <div class="operation_icon">-->
          <!--            <el-button v-if="scope.row.void_cheque_id" size="mini" type="" @click="onEdit(scope)">-->
          <!--              {{ $t('daily.cheque.button.chequeRestore') }}-->
          <!--            </el-button>-->
          <!--            <el-button v-else size="mini" type="" @click="onEdit(scope)">-->
          <!--              {{ $t('daily.cheque.button.chequeVoid') }}-->
          <!--            </el-button>-->
          <!--          </div>-->
          <!--        </template>-->
        </ETable>
      </div>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear, fetchPeriods } from '@/api/master/years'
import { fetchAccounts } from '@/api/master/account'
import { getChequeList, chequeVoid, chequeVoidPeriod } from '@/api/daily/cheques'
import { fetchChequeBooks } from '@/api/assistance/chequeBook'
import ETable from '@/components/ETable'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'

import { amountFormat } from '@/utils'

import ENumeric from '@/components/ENumeric'
import { chequeListExport } from '@/api/report/excel'
import { exportExcel } from '@/utils/excel'
import handlePDF from './handlePDF.vue'
export default {
  name: 'DailyChequeIndex',
  components: {
    ETable,
    customStyle,
    ENumeric,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      isAllMonth: true,
      isMonth: false,
      typeOption: {
        X: this.$t('daily.cheque.typeOption.type_X'),
        N: this.$t('daily.cheque.typeOption.type_N'),
        C: this.$t('daily.cheque.typeOption.type_C'),
      },
      bankList: [],
      chequeBooks: [],
      years: '',
      selectedYearId: '',
      tableData: [],
      data: [],
      monthList: [],
      langKey: 'daily.cheque.label.',
      tableColumns: ['chq_no', 'vc_chq_date', 'vc_payee', 'vc_amount', 'vc_no'],
      amountColumns: ['vc_amount'],
      preferences: {
        filters: {
          selectedAcCode: '',
          selectedType: 'X',
          selectedYearCode: '',
          selectedMonth: '',
          selectedChequeCode: '',
        },
      },
      childPreferences: ['selectedMonth'],
      periods: [],
      printing: false,
      multipleSelection: [],
    }
  },
  computed: {
    ...mapGetters(['language', 'user_id']),
    allMonth() {
      return {
        label: this.$t('filters.all_months'),
        value: '',
      }
    },
    selectType() {
      return this.preferences.filters.selectedType
    },
  },
  watch: {
    // selectedYearId: function(currentVal) {
    //   this.years.forEach(ele => {
    //     if (currentVal === ele.fy_id) {
    //       this.preferences.filters.selectedYearCode = ele.fy_code
    //     }
    //   })
    //   this.getMonth().then(this.reloadData)
    // },
    // 'preferences.filters.selectedAcCode': function(currentVal) {
    // this.getChequeBooks().then(() => this.reloadData())
    // }
  },
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  methods: {
    selectable(row) {
      const str = (row.vc_amount || '').replace(/,/g, '')
      const num = Number(str)
      return !isNaN(num) && num > 0
    },
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchAccounts({ ac_bank: 'C' }))
        .then(res => {
          this.bankList = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYear !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          if (this.preferences.filters.selectedAcCode !== '') {
            let bool = false
            this.bankList.forEach(ele => {
              if (this.preferences.filters.selectedAcCode === ele.ac_code) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedAcCode = this.bankList[0].ac_code
            }
          } else {
            this.preferences.filters.selectedAcCode =
              this.bankList && this.bankList.length > 0 ? this.bankList[0].ac_code : ''
          }
        })
        .then(this.getChequeBooks)
        .then(this.getMonth)
        .then(this.fetchPeriods)
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            let bool = false
            this.monthList.forEach(i => {
              if (i.value === this.preferences.filters.selectedMonth) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedMonth = this.monthList[0].value
            }
          }
        })
        .then(this.reloadData)
        .finally(() => {
          this.loading = false
        })
    },
    getChequeBooks() {
      return new Promise((resolve, reject) => {
        this.chequeBooks = []
        this.preferences.filters.selectedChequeCode = ''
        if (this.preferences.filters.selectedAcCode !== '') {
          const ac_code = this.preferences.filters.selectedAcCode
          fetchChequeBooks({ ac_code })
            .then(res => {
              // res.forEach((ele, i) => {
              //   if (i === 0) {
              //     this.preferences.filters.selectedChequeCode = ele.chqbk_code
              //   }
              //   // this.chequeBooks.push({ label: ele.chqbk_code, value: ele.chqbk_code })
              // })
              if (!this.preferences.filters.selectedChequeCode && res.length) {
                this.preferences.filters.selectedChequeCode = res[0].chqbk_code
              }
              this.chequeBooks = res
              resolve()
            })
            .catch(() => {
              this.preferences.filters.selectedChequeCode = ''
              resolve()
            })
        } else {
          this.preferences.filters.selectedChequeCode = ''
          resolve()
        }
      })
    },
    onChangeType(val) {
      switch (val) {
        case 'X':
        case 'N':
          this.preferences.filters.selectedChequeCode = ''
          this.reloadData()
          break
        case 'C':
          this.getChequeBooks().then(this.reloadData)
          break
      }
    },
    changeBank() {
      this.getChequeBooks().then(this.reloadData)
    },
    onChangeYear(fy_id) {
      const item = this.years.find(i => i.fy_id === fy_id)
      if (item) {
        this.preferences.filters.selectedYearCode = item.fy_code
        this.preferences.filters.selectedMonth = ''
        this.getMonth().then(this.reloadData)
      }
    },
    getMonth() {
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            res.periods.forEach(ele => {
              this.monthList.push({ label: ele.pd_name, value: ele.pd_code, status: ele.pd_status })
            })
            resolve()
          })
        }
      })
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        if (this.preferences.filters.selectedAcCode === '') {
          this.tableData = []
          reject()
          return
        }
        this.loading = true
        const filter_date = {
          ac_code: '',
          type: '',
          fy_code: '',
          pd_code: '',
          chqbk_code: undefined,
        }
        filter_date.ac_code = this.preferences.filters.selectedAcCode
        filter_date.type = this.preferences.filters.selectedType
        filter_date.fy_code = this.preferences.filters.selectedYearCode
        filter_date.pd_code = this.preferences.filters.selectedMonth
        if (filter_date.type === 'C') {
          filter_date.chqbk_code = this.preferences.filters.selectedChequeCode
            ? this.preferences.filters.selectedChequeCode
            : undefined
        }
        if (filter_date.type === 'C' && !filter_date.chqbk_code) {
          this.loading = false
          this.tableData = []
          reject()
          return
        }
        getChequeList(filter_date)
          .then(res => {
            const data = this.formatData(res)
            this.tableData = data
            resolve(data)
          })
          .catch(err => {
            this.tableData = []
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    formatData(data) {
      const newData = []
      const amountColumns = this.amountColumns
      data.forEach(item => {
        const newItem = Object.assign({}, item)
        amountColumns.forEach(col => {
          if (newItem.hasOwnProperty(col)) {
            newItem[col] = amountFormat(newItem[col])
          }
        })
        newItem['void'] = !newItem.void_cheque_id // 是否作廢
        newData.push(newItem)
      })
      return newData
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    onEdit({ row }) {
      if (this.preferences.filters.selectedType !== 'C') {
        return
      }
      console.log(row)
      // this.$set(row, 'edit', true)
      // const v = !!row.void_cheque_id
      const chq_no = row.chq_no
      const chqbk_code = this.preferences.filters.selectedChequeCode
      chequeVoid({
        chq_no,
        chqbk_code,
      }).then(res => {
        console.log('chequeVoid', res)
        // 應該返回ID
        this.$set(row, 'void_cheque_id', res.void_cheque_id)
      })
    },
    fetchPeriods() {
      const chqbk = this.chequeBooks.find(
        i => i.chqbk_code === this.preferences.filters.selectedChequeCode,
      )
      if (!chqbk || !chqbk.active_year) return

      this.tableLoading = true
      const active_year = chqbk.active_year
      return new Promise((resolve, reject) => {
        fetchPeriods(active_year)
          .then(res => {
            this.periods = res
            resolve()
          })
          .finally(() => {
            this.tableLoading = false
          })
      })
    },
    handleVoid({ row }) {
      const void_cheque_id = row.void_cheque_id
      const pd_code = row.pd_code
      chequeVoidPeriod({
        void_cheque_id,
        pd_code,
      }).then(res => {
        console.log('chequeVoidPeriod', res)
      })
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id

      const ac_code = this.preferences.filters.selectedAcCode
      const type = this.preferences.filters.selectedType
      const fy_code = this.preferences.filters.selectedYearCode
      const pd_code = this.preferences.filters.selectedMonth
      let chqbk_code
      if (type === 'C') {
        chqbk_code = this.preferences.filters.selectedChequeCode
          ? this.preferences.filters.selectedChequeCode
          : undefined
      }

      if (!user_id || (type === 'C' && !chqbk_code)) {
        // this.$message.error('')
        return
      }
      this.loading = true
      chequeListExport({ user_id, export_type, ac_code, type, fy_code, pd_code, chqbk_code })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  height: 25px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 25px;
  }
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        line-height: 30px;
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
      .vc_chq_date {
        margin: 2px 0;
        .cell {
          height: 27px;
          line-height: 27px;
          padding: 0;
          .el-select {
            width: 100%;
          }
        }
      }
      .chq_no_cell {
        cursor: pointer;
        &.void {
          color: #b30000;
        }
      }
    }
  }
  .cheque-table {
    height: calc(100vh - 190px);

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
      }
    }
  }
}
</style>
