<template>
  <div id="account-select">
    <el-row>
      <el-row v-if="selectedAccounts.length > 0" :span="24" class="tool-row">
        <div class="actions">
          <el-button
            type="primary"
            size="mini"
            class="new-row"
            icon="el-icon-plus"
            @click="onAddRow"
          >
            <!--{{ $t('button.add') }}-->
          </el-button>
        </div>
      </el-row>
      <el-col :span="24" class="table-row">
        <el-table
          :show-header="false"
          :data="selectedAccounts"
          v-bind="$attrs"
          class="account-table"
          style="height: auto"
        >
          <el-table-column :label="$t('table.index')" type="index" width="50" align="right" />
          <el-table-column class-name="account" :label="$t('table.account')" min-width="150" align="left">
            <template v-if="scope && scope.row" slot-scope="scope">
              <el-select v-model="scope.row.account_id" style="width: 100%">
                <el-option
                  v-for="item in accounts"
                  :key="item.account_id"
                  :value="item.account_id"
                  :label="`[${item.ac_code}] ${
                    item[language === 'en' ? 'ac_name_en' : 'ac_name_cn']
                  }`"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="$t('table.action')" align="left" width="50">
            <template v-if="scope && scope.row" slot-scope="scope">
              <span>
                <i class="el-icon-delete" @click="removeRow(scope.row)" />
              </span>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="bottom-action">
              <el-button
                type="primary"
                size="mini"
                class="new-row"
                icon="el-icon-plus"
                @click="onAddRow"
              >
                <!--{{ $t('button.add') }}-->
              </el-button>
            </div>
          </template>
          <!--          <template slot="append">-->
          <!--            <div class="bottom-action">-->
          <!--              <el-button type="primary" size="mini" class="new-row" @click="onAddRow">add</el-button>-->
          <!--              &lt;!&ndash;          <i class="el-icon-plus" @click="onAddRow"/>&ndash;&gt;-->
          <!--            </div>-->
          <!--          </template>-->
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { searchAccounts } from '@/api/master/account'

export default {
  name: 'BudgetAccountSelect',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    fyCode: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      accounts: [],
    }
  },
  computed: {
    ...mapGetters(['language']),
    selectedAccounts: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
  },
  created() {
    const fy_code = this.fyCode
    searchAccounts({ fy_code }).then(res => {
      this.accounts = res
    })
  },
  methods: {
    onAddRow() {
      const item = {
        account_id: '',
      }
      this.selectedAccounts.push(item)
    },
    removeRow(row) {
      const data_index = this.selectedAccounts.findIndex(i => i.account_id === row.account_id)
      data_index > -1 && this.selectedAccounts.splice(data_index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.new-row {
  padding-top: 5px;
  padding-bottom: 5px;
}
.actions {
  text-align: center;
  /*float: right;*/
  /*z-index: 1;*/
  /*position: absolute;*/
  /*right: 0;*/
  /*width: 480px;*/
}
.el-icon-delete {
  cursor: pointer;
}
.bottom-action {
  text-align: center;
}
#account-select {
  /deep/ {
    .el-table {
      background: transparent;
    }
    .el-table__empty-block {
      min-height: 35px;
    }
    .el-table__body-wrapper {
      height: auto;
    }
    .el-select {
      input {
        height: 23px;
      }
    }
    .el-input__suffix .el-input__suffix-inner i {
      height: 22px;
      line-height: 22px;
    }
  }
}
.table-row,
.tool-row {
  position: relative;
}
</style>
