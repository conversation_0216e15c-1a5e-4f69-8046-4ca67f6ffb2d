<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <!-- 會計週期 -->
        <el-select v-model="preferences.filters.year" class="year" @change="fetchTree">
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>

        <svg-icon v-if="hasPermission_Add" icon-class="new_folder" @click="onAddType" />
        <svg-icon v-if="hasPermission_Add" icon-class="new_file" @click="onAdd" />
        <div v-for="i in staffLevel" :key="i" class="selectLevel" @click="expandLevel(i)">
          {{ i }}
        </div>
        <div class="selectLevel" @click="enableYear">
          {{ preferences.filters.isEnableYear ? 'N' : 'Y' }}
        </div>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 按鈕 -->
          <div
            v-if="hasPermission_Input"
            :title="$t('btnTitle.importExcel')"
            class="icon import"
            @click="importDialog = true"
          >
            <svg-icon icon-class="import" class="action-icon" />
          </div>
          <!--  -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcel')"
            class="icon export"
            @click="onExport"
          >
            <svg-icon icon-class="export" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <tree-table
          ref="TreeTable"
          :data="staffTree"
          :eval-func="func"
          :eval-args="args"
          :expand-all="true"
          :first-field="language === 'en' ? 'name_en' : 'name_cn'"
          :first-field-align="firstFieldAlign"
          :first-field-width="firstFieldWidth"
          number-field="code"
          folder-field="st_type_id"
          border
          @changeWidth="changeColumnWidth"
          @changeExpanded="onChangeExpanded"
        >
          <!--    編號姓名      -->
          <template v-if="scope && scope.row" slot="firstField" slot-scope="{ scope }">
            <i
              v-if="scope.row.st_type_id"
              :class="
                'edac-icon edac-icon-folder' +
                  (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year)
                    ? ' activate'
                    : '')
              "
            />
            <i v-else class="edac-icon edac-icon-file" />
            <span
              v-if="!scope.row.st_type_id"
              :class="
                'number-field' +
                  (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year)
                    ? ' selected'
                    : '')
              "
            >{{ scope.row.code }}</span>
            <span class="key">{{ scope.row[language === 'en' ? 'name_en' : 'name_cn'] }}</span>
          </template>
          <!-- 其餘字段 -->
          <!--          <el-table-column-->
          <!--            :label="$t('assistance.staff.label.username')"-->
          <!--            column-key="username"-->
          <!--          >-->
          <!--            <template v-if="scope && scope.row" slot-scope="scope">-->
          <!--              <span v-if="scope.row.staff_id">-->
          <!--                {{ scope.row.username }}</span>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <!--          <el-table-column-->
          <!--            :label="$t('assistance.staff.label.grade')"-->
          <!--            column-key="grade"-->
          <!--          >-->
          <!--            <template v-if="scope && scope.row" slot-scope="scope">-->
          <!--              {{ scope.row.st_grade && scope.row.st_grade.replace(/,/g ,'') }}-->
          <!--            </template>-->
          <!--          </el-table-column>-->

          <el-table-column
            v-for="item in filteredStyleColumns"
            :key="item.ss_id"
            :label="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :width="item.width"
            :property="item.ss_key"
            :column-key="item.ss_key"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span v-if="item.ss_key === 'grade'">
                {{ scope.row.st_grade && scope.row.st_grade.replace(/,/g, '') }}
              </span>
              <span v-else>
                {{ scope.row[item.ss_key] }}
              </span>
            </template>
          </el-table-column>
          <!--操作列-->
          <el-table-column
            :label="$t('table.action')"
            align="left"
            header-align="left"
            min-width="180"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <div v-if="scope.row.st_type_id" class="operation_icon">
                <svg-icon
                  v-if="hasPermission_Add"
                  icon-class="new_folder"
                  @click="onAddType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <svg-icon v-if="hasPermission_Add" icon-class="new_file" @click="onAdd(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditType(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i
                  v-if="
                    hasPermission_Delete && (!scope.row.children || scope.row.children.length === 0)
                  "
                  class="el-icon-close"
                  @click="onDeleteType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
              </div>
              <div v-else class="operation_icon">
                <svg-icon class="no-cursor" icon-class="" />
                <svg-icon class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
                <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
              </div>
            </template>
          </el-table-column>
        </tree-table>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-object="editObject"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        :table-data="staffTree"
        @onCancel="onViewCancel"
      />
      <addTypePage
        v-else-if="leftView === 'addStaffType' || leftView === 'editStaffType'"
        :edit-object="editTypeObject"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        :table-data="staffTree"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      table-type="tree"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      class="upload-dialog"
      width="450px"
    >
      <UploadExcel :on-success="onImport" :on-template="onExport" />
    </el-dialog>
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import addPage from './add'
import addTypePage from './addType'
import { deleteStaff, getStaffsTree, exportStaffs, importStaffs } from '@/api/assistance/staff'
import { deleteStaffType } from '@/api/assistance/staff/staffType'
import { mapGetters } from 'vuex'
import treeToArray from '@/components/TreeTable/eval.js'
import treeTable from '@/components/TreeTable'
import UploadExcel from '@/components/UploadExcel/index'
import { fetchYears } from '@/api/master/years'
// 樣式
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
// 權限
import mixinPermission from '@/views/mixins/permission'
// 偏好
import loadPreferences from '@/views/mixins/loadPreferences'

// 導出Excel
import { exportExcel, importExcel } from '@/utils/excel'

export default {
  name: 'AssistanceStaffIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    addTypePage,
    treeTable,
    UploadExcel,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      leftView: '',
      funds: [],
      editObject: null,
      editTypeObject: null,
      func: treeToArray,
      expandAll: false,
      editParent: null,
      args: [null, null, 'timeLine'],
      staffTree: [],
      years: [],
      selectedYear: '',
      staffLevel: 0,
      currentLevel: 99,
      isEnableYear: false,
      importDialog: false,
      loading: false,

      langKey: 'assistance.staff.label.',
      tableColumns: ['username', 'password', 'grade'],

      preferences: {
        filters: {
          year: '',
          currentLevel: 99,
          isEnableYear: false,
          expandedList: '',
        },
      },
      exportFileName: 'staffs', // 導出文件名
    }
  },
  computed: {
    ...mapGetters(['language']),
    filteredStyleColumns() {
      return this.styleColumns.filter(item => item.ss_key !== 'first_field')
    },
  },
  created() {},
  mounted() {
    this.fetchData()
  },
  methods: {
    expandLevel(level) {
      if (level) {
        this.$refs.TreeTable.showLevel(level)
        this.preferences.filters.currentLevel = level
        this.preferences.filters.expandedList = ''
      } else {
        this.$refs.TreeTable.setExpandItem(this.preferences.filters.expandedList)
      }
    },
    handleMaxLevel() {
      this.$nextTick(() => {
        this.staffLevel = this.$refs.TreeTable.getMaxLevel()
      })
    },
    enableYear() {
      this.preferences.filters.isEnableYear = !this.preferences.filters.isEnableYear
      this.fetchTree()
    },
    onAdd(scope) {
      this.editObject = null
      this.editTypeObject = null
      this.editParent = scope && scope.row
      this.leftView = 'add'
    },
    onAddType(scope) {
      this.editObject = null
      this.editTypeObject = null
      this.editParent = scope && scope.row
      this.leftView = 'addStaffType'
    },
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'edit'
    },
    onEditType(scope) {
      this.editTypeObject = scope.row
      this.leftView = 'editStaffType'
    },
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const staff_id = scope.row.staff_id
          return new Promise((resolve, reject) => {
            deleteStaff(staff_id)
              .then(res => {
                if (this.editObject && this.editObject.staff_id === staff_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    onDeleteType(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const st_type_id = scope.row.st_type_id
          return new Promise((resolve, reject) => {
            deleteStaffType(st_type_id)
              .then(res => {
                if (this.editTypeObject && this.editTypeObject.st_type_id === st_type_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchYears()
        .then(res => {
          this.years = res
        })
        // 加載偏好設置
        .then(this.loadUserPreference)
        .then(() => {
          if (!this.preferences.filters.year) {
            this.preferences.filters.year =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          } else {
            if (this.years && this.years.length > 0) {
              let bool = false
              this.years.forEach(i => {
                if (i.fy_code === this.preferences.filters.year) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.year = this.years[0].fy_code
              }
            }
          }
        })
        .then(() => {
          return getStaffsTree(
            this.preferences.filters.isEnableYear ? this.preferences.filters.year : '',
          )
        })
        .then(res => {
          this.staffTree = res
          this.handleMaxLevel()
          this.$nextTick(() => {
            this.expandLevel(this.preferences.filters.currentLevel)
          })
        })
        .catch(() => {})
    },
    fetchTree() {
      this.leftView = null
      this.editObject = null
      this.editParent = null
      this.editTypeObject = null
      getStaffsTree(
        this.preferences.filters.isEnableYear ? this.preferences.filters.year : '',
      ).then(res => {
        this.staffTree = res
        this.handleMaxLevel()
        this.$nextTick(() => {
          this.expandLevel(this.preferences.filters.currentLevel)
        })
      })
    },
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.fetchTree()
      }
    },
    onExport() {
      if (this.loading) {
        return
      }
      this.loading = true
      exportStaffs()
        .then(res => exportExcel(res, this.exportFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onImport({ results, header }) {
      this.loading = true
      importExcel(this, importStaffs, results, header)
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
    onChangeExpanded(listStr) {
      this.preferences.filters.expandedList = listStr
      this.preferences.filters.currentLevel = 0
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .year {
    width: 110px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.tree-table {
  .operation_icon {
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
</style>
