<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 賬目編號 -->
      <el-form-item
        :rules="codeRules"
        :label="$t('master.account.label.ac_code')"
        :class="{ 'code-rules-error': haveExist }"
        prop="ac_code"
      >
        <el-input v-model="form.ac_code" clearable />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item :rules="rules" :label="$t('master.account.label.ac_name_cn')" prop="ac_name_cn">
        <el-input v-model="form.ac_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item :rules="rules" :label="$t('master.account.label.ac_name_en')" prop="ac_name_en">
        <el-input v-model="form.ac_name_en" clearable />
      </el-form-item>

      <!-- 中文簡稱 -->
      <el-form-item :rules="rules" :label="$t('master.account.label.ac_abbr_cn')" prop="ac_abbr_cn">
        <el-input v-model="form.ac_abbr_cn" clearable />
      </el-form-item>

      <!-- 英文簡稱 -->
      <el-form-item :rules="rules" :label="$t('master.account.label.ac_abbr_en')" prop="ac_abbr_en">
        <el-input v-model="form.ac_abbr_en" clearable />
      </el-form-item>

      <!-- 上級  更改上級 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.account.label.parent_fund_id')"
        prop="parent_fund_id"
      >
        <el-select
          v-model="form.parent_fund_id"
          :placeholder="$t('placeholder.select')"
          @change="changeParentType"
        >
          <el-option
            v-for="item in accountTypes"
            :key="item.value"
            :value="item.fund_id"
            :label="conversionParentAccountType(item)"
            v-html="conversionParentAccountType(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- 位置(前置) -->
      <el-form-item :label="$t('master.account.label.seq')" prop="seq">
        <el-select v-model="form.seq" :placeholder="$t('placeholder.select')" @change="onChangeSeq">
          <el-option
            v-for="item in sepOptions"
            :key="item.fund_id || item.account_id"
            :label="language === 'en' ? item.name_en : item.name_cn"
            :value="item.fund_id || item.account_id"
          >
            {{ language === 'en' ? item.name_en : item.name_cn }}
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 賬目性質 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.account.label.nature_code')"
        prop="nature_code"
      >
        <el-select v-model="form.nature_code" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in natureOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <!-- {{ language === 'en' ? item.name_en : item.name_cn }} -->
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 賬目類別 -->
      <el-form-item :label="$t('master.account.label.ac_category')" prop="ac_category">
        <el-checkbox v-model="form.ac_B">
          {{ $t('master.account.category.B') }}
        </el-checkbox>
        <el-checkbox v-model="form.ac_I">
          I
        </el-checkbox>
        <el-checkbox v-model="form.ac_E">
          E
        </el-checkbox>
      </el-form-item>
      <!-- 銀行 -->
      <el-form-item :rules="rules" :label="$t('master.account.label.ac_bank')" prop="ac_bank">
        <el-select v-model="form.ac_bank" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in bankOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 年結賬號 -->
      <el-form-item
        v-if="!form.ac_B"
        :rules="{
          required: !form.ac_B,
          trigger: 'blur',
          message: ' ',
        }"
        :label="$t('master.account.label.ac_cf')"
        prop="ac_cf"
      >
        <el-select v-model="form.ac_cf" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in accountList"
            :key="item.account_id"
            :label="language === 'en' ? item.ac_name_en : item.ac_name_cn"
            :value="item.ac_cf"
          >
            {{ language === 'en' ? item.ac_name_en : item.ac_name_cn }}
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 組別 -->
      <el-form-item :label="$t('master.account.label.ac_group')" prop="ac_group">
        <el-input v-model="form.ac_group" :placeholder="$t('master.account.label.ac_group')" />
      </el-form-item>

      <!-- 活躍年度 -->
      <el-form-item :label="$t('master.account.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 收入預算 -->
      <el-form-item :label="$t('master.account.label.income_bg_year')">
        <el-checkbox-group v-model="income_bg_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- 支出預算 -->
      <el-form-item :label="$t('master.account.label.expense_bg_year')">
        <el-checkbox-group v-model="expense_bg_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  createAccount,
  updateAccount,
  getAccountTreeNode,
  getAccount,
  fetchAccounts,
} from '@/api/master/account'
import { fetchFunds, getFund } from '@/api/master/funds'
import { fetchYears } from '@/api/master/years'

export default {
  name: 'MasterAccountAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    defaultParent: {
      type: Number,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      system: 'BG',
      form: {
        fund_id: '',
        parent_fund_id: '',
        account_id: '',
        ac_code: '',
        ac_name_cn: '',
        ac_name_en: '',
        ac_abbr_cn: '',
        ac_abbr_en: '',
        nature_code: '',
        fund_type: 'G', // 賬目類別為G
        ac_B: '',
        ac_I: '',
        ac_E: '',
        ac_cf: '',
        ac_bank: '',
        ac_group: '',
        active_year: '',
        income_bg_year: '',
        expense_bg_year: '',
        seq: '',
      },
      defaultForm: {
        fund_id: '',
        parent_fund_id: '',
        account_id: '',
        ac_code: '',
        ac_name_cn: '',
        ac_name_en: '',
        ac_abbr_cn: '',
        ac_abbr_en: '',
        nature_code: '',
        fund_type: 'G', // 賬目類別為G
        ac_B: '',
        ac_I: '',
        ac_E: '',
        ac_cf: '',
        ac_bank: '',
        ac_group: '',
        active_year: '',
        income_bg_year: '',
        expense_bg_year: '',
        seq: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      defaultSepOptions: [
        {
          fund_id: '',
          account_id: '',
          name_cn: this.$t('master.account.label.seq_default'),
          name_en: this.$t('master.account.label.seq_default'),
        },
      ],
      sepOptions: [
        {
          fund_id: '',
          account_id: '',
          name_cn: this.$t('master.account.label.seq_default'),
          name_en: this.$t('master.account.label.seq_default'),
        },
      ],
      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      defaultAccountTypes: [
        {
          fund_id: '',
          fund_name_cn: this.$t('master.account.label.parent_default'),
          fund_name_en: this.$t('master.account.label.parent_default'),
        },
      ],
      accountTypes: [],
      defaultAccountList: [
        {
          ac_cf: '',
          ac_name_cn: this.$t('master.account.label.cf_default'),
          ac_name_en: this.$t('master.account.label.cf_default'),
        },
      ],
      accountList: [],
      natureOptions: [
        {
          value: 'G',
          label: this.$t('master.account.natureOptions.option_G'),
        },
        {
          value: 'I/E',
          label: this.$t('master.account.natureOptions.option_IE'),
        },
        {
          value: 'I',
          label: this.$t('master.account.natureOptions.option_I'),
        },
        {
          value: 'E',
          label: this.$t('master.account.natureOptions.option_E'),
        },
        {
          value: 'Dr',
          label: this.$t('master.account.natureOptions.option_Dr'),
        },
        {
          value: 'Cr',
          label: this.$t('master.account.natureOptions.option_Cr'),
        },
      ],
      bankOptions: [
        {
          value: 'X',
          label: this.$t('master.account.bankOptions.option_X'),
        },
        {
          value: 'S',
          label: this.$t('master.account.bankOptions.option_S'),
        },
        {
          value: 'C',
          label: this.$t('master.account.bankOptions.option_C'),
        },
        {
          value: 'F',
          label: this.$t('master.account.bankOptions.option_F'),
        },
        {
          value: 'P',
          label: this.$t('master.account.bankOptions.option_P'),
        },
        {
          value: 'A',
          label: this.$t('master.account.bankOptions.option_A'),
        },
      ],
      seqStartLevel: 1,
      active_year_arr: [],
      income_bg_year_arr: [],
      expense_bg_year_arr: [],
      loading: true,
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
    codeRules() {
      return [
        {
          required: true,
          validator: this.checkCode,
          trigger: 'blur',
        },
      ]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    editParent() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    changeParentType() {
      if (!this.form.parent_fund_id) {
        this.form.seq = ''
        return
      }
      getAccountTreeNode(
        '',
        this.form.parent_fund_id,
        this.form.fund_id,
        this.form.account_id,
      ).then(res => {
        this.sepOptions = [...res, ...this.defaultSepOptions]
        if (this.editObject && this.form.seq_i) {
          let isLast = true
          for (let i = 0; i < res.length; i++) {
            if (res[i].seq > this.form.seq_i) {
              isLast = false
              this.form.seq = res[i].fund_id ? res[i].fund_id : res[i].account_id
              break
            }
          }
          if (isLast) {
            this.form.seq = ''
          }
        } else {
          this.form.seq = ''
        }
      })
    },
    checkRequired(rule, value, callback) {},
    setParent() {
      if (this.editParent && this.editParent.fund_id) {
        this.form.parent_fund_id = this.editParent.fund_id
      } else if (this.editObject && this.editObject.parent && this.editObject.parent.fund_id) {
        this.form.parent_fund_id = this.editObject.parent.fund_id
      } else {
        this.form.parent_fund_id = this.defaultParent // index 傳入的fund_id
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          this.active_year_arr = []
          this.income_bg_year_arr = []
          this.expense_bg_year_arr = []
          // this.form = Object.assign({}, this.editObject)
          getAccount(this.editObject.account_id)
            .then(res => {
              this.form = Object.assign({}, res)
              this.setParent()
              this.form.parent_fund_id =
                res.fundAccountRelation && res.fundAccountRelation.parent_fund_id
                  ? res.fundAccountRelation.parent_fund_id
                  : ''
              this.form.seq_i = res.fundAccountRelation ? res.fundAccountRelation.seq : ''
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              this.income_bg_year_arr = res.income_bg_year
                ? res.income_bg_year.split(',')
                : ''.split(',')
              this.expense_bg_year_arr = res.expense_bg_year
                ? res.expense_bg_year.split(',')
                : ''.split(',')
              res.ac_B === 'Y' ? (this.form.ac_B = true) : (this.form.ac_B = false)
              res.ac_I === 'Y' ? (this.form.ac_I = true) : (this.form.ac_I = false)
              res.ac_E === 'Y' ? (this.form.ac_E = true) : (this.form.ac_E = false)
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.active_year_arr = []
          this.income_bg_year_arr = []
          this.expense_bg_year_arr = []
          this.form = Object.assign({}, this.defaultForm)
          this.setParent()
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => getFund(this.defaultParent))
        .then(res => {
          this.defaultAccountTypes = [
            {
              fund_id: this.defaultParent,
              fund_name_cn: res.fund_name_cn,
              fund_name_en: res.fund_name_en,
            },
          ]
          return Promise.resolve()
        })
        .then(() =>
          fetchFunds({
            fund_type: 'G',
            parent_fund_id: this.defaultParent,
            // fund_id: this.form.fund_id
          }),
        )
        .then(res => {
          this.accountTypes = [...this.defaultAccountTypes, ...res]
          this.changeParentType(this.form.parent_fund_id)
        })
        .then(() => fetchAccounts({ ac_B: 'Y', parent_fund_id: this.defaultParent }))
        .then(res => {
          this.accountList = res
        })
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editObject) {
            const selected = res.find(i => i.fy_code === this.fyCode)
            if (selected) {
              this.active_year_arr = [selected.fy_code]
              this.income_bg_year_arr = [selected.fy_code]
              this.expense_bg_year_arr = [selected.fy_code]
            }
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 1) {
          return this.sepOptions[this.sepOptions.length - 2].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 1) {
          const seq_ele = this.sepOptions.find(i => {
            const key = i.fund_id ? 'fund_id' : 'account_id'
            return i[key] === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const account_id = this.form.account_id
        const ac_code = this.form.ac_code
        const ac_name_cn = this.form.ac_name_cn
        const ac_name_en = this.form.ac_name_en
        const ac_abbr_cn = this.form.ac_abbr_cn
        const ac_abbr_en = this.form.ac_abbr_en
        const nature_code = this.form.nature_code
        const ac_bank = this.form.ac_bank
        const ac_group = this.form.ac_group
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const income_bg_year = this.income_bg_year_arr.filter(i => i).join(',')
        const expense_bg_year = this.expense_bg_year_arr.filter(i => i).join(',')
        const parent_fund_id = this.form.parent_fund_id
        const ac_B = this.form.ac_B ? 'Y' : 'N'
        const ac_I = this.form.ac_I ? 'Y' : 'N'
        const ac_E = this.form.ac_E ? 'Y' : 'N'
        const ac_cf = ac_B === 'Y' ? undefined : this.form.ac_cf || undefined

        const seq = this.newSeq()

        if (this.editObject) {
          // 編輯
          updateAccount({
            account_id,
            ac_code,
            ac_name_cn,
            ac_name_en,
            ac_abbr_cn,
            ac_abbr_en,
            nature_code,
            ac_B,
            ac_I,
            ac_E,
            ac_bank,
            ac_group,
            active_year,
            income_bg_year,
            expense_bg_year,
            parent_fund_id,
            seq,
            ac_cf,
          })
            .then(() => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createAccount(
            ac_code,
            ac_name_cn,
            ac_name_en,
            ac_abbr_cn,
            ac_abbr_en,
            nature_code,
            ac_B,
            ac_I,
            ac_E,
            ac_bank,
            ac_group,
            active_year,
            income_bg_year,
            expense_bg_year,
            parent_fund_id,
            seq,
            ac_cf,
          )
            .then(() => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    onChangeSeq() {
      this.$forceUpdate()
    },
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        console.log('checkCode', !value)
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      const codeArr = this.getCodeArr(this.tableData)
      console.log('codeArr', codeArr)
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editObject && this.editObject.code === i.code) {
          return
        }
        codeArr.push(i.code)
        if (i.children) {
          codeArr.push(...this.getCodeArr(i.children))
        }
      })
      return codeArr
    },
  },
}
</script>

<style lang="scss" scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
