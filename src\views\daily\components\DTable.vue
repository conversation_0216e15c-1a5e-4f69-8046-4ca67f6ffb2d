<template>
  <!--  傳票表格  -->
  <div>
    <el-table
      ref="DTable"
      :data="data"
      :row-class-name="isStripe"
      v-bind="$attrs"
      :class="'d-table' + (isView ? ' is-view' : '')"
      border
      @header-dragend="onHeaderDragend"
    >
      <el-table-column class-name="check-box" type="index" label="#" width="50">
        <template v-if="scope && scope.row" slot-scope="scope">
          <el-checkbox
            v-if="action !== 'view'"
            v-model="scope.row._checked"
            :label="scope.$index + 1"
          />
          <span v-else>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <slot name="columns">
        <el-table-column
          :label="$t('daily.label.account')"
          :resizable="false"
          width="175"
          align="left"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div class="td-account">
              <el-input
                v-model="scope.row.ac_code"
                :readonly="isView"
                :class="[
                  {
                    'is-error': scope.row.ac_is_error ? true : false,
                    link: isView && scope.row.ac_code,
                    none: isView && !scope.row.ac_code,
                  },
                ]"
                :placeholder="$t('placeholder.ac_code')"
                style="width: 80px"
                @blur="onAcCodeBlur(scope.row, scope.$index)"
                @click.native="onAcCodeClick(scope.row, scope.$index)"
              />
              <i
                v-if="!isView"
                class="edac-icon action-icon edac-icon-search"
                @click="onSelectAccount(scope.row, scope.$index)"
              />
              <span v-if="isView">
                <i
                  :class="'edac-icon edac-icon-I' + (scope.row.tx_type === 'I' ? '' : ' disable')"
                />
                <i
                  :class="'edac-icon edac-icon-E' + (scope.row.tx_type === 'E' ? '' : ' disable')"
                />
              </span>
              <el-radio-group
                v-else
                v-model="scope.row.tx_type"
                :disabled="scope.row.tx_type_disable"
              >
                <el-radio :disabled="!scope.row.tx_type_has_I" label="I" />
                <el-radio :disabled="!scope.row.tx_type_has_E" label="E" />
              </el-radio-group>
              <el-input
                :class="[
                  {
                    'is-error': scope.row.ac_is_error ? true : false,
                    link: isView && scope.row.ac_code,
                    none: isView && !scope.row.ac_code,
                  },
                ]"
                :value="language === 'en' ? scope.row.ac_name_en : scope.row.ac_name_cn"
                :placeholder="$t('placeholder.ac_name')"
                readonly
                style="width: 150px"
                @click.native="onAcCodeClick(scope.row, scope.$index)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('daily.label.descr')"
          :resizable="false"
          min-width="200"
          align="left"
        >
          <!--          <template slot="header" slot-scope="scope">-->
          <!--            <span>-->
          <!--              <el-row style="line-height: 23px;width: 100%">-->
          <!--                <el-col :span="18">-->
          <!--                  描述-->
          <!--                </el-col>-->
          <!--                <el-col :span="6">-->
          <!--                  <span style="float: right;">-->
          <!--                    <el-checkbox label="展開"></el-checkbox>-->
          <!--                  </span>-->
          <!--                </el-col>-->
          <!--              </el-row>-->
          <!--            </span>-->
          <!--          </template>-->
          <template v-if="scope && scope.row" slot-scope="scope">
            <div v-if="resetDescView" class="td-desc">
              <el-row class="desc-row">
                <!--描述-->
                <i
                  :class="{
                    disable: !scope.row.show_desc,
                    'action-icon': action !== 'view',
                  }"
                  :title="$t('placeholder.desc')"
                  class="edac-icon edac-icon-company"
                  @click="handleSwitchIconDisable(scope, 'desc')"
                />
                <el-input
                  v-show="scope.row.show_desc"
                  v-model="scope.row.descr"
                  :readonly="isView"
                  :placeholder="$t('placeholder.desc')"
                  :class="[
                    {
                      'is-new': scope.row.desc_is_new ? true : false,
                    },
                  ]"
                  :title="scope.row.descr || $t('placeholder.desc')"
                  class="desc-input"
                  @blur="onDescBlur(scope.row, scope.$index)"
                />
                <i
                  v-if="!isView"
                  v-show="scope.row.show_desc"
                  class="edac-icon action-icon edac-icon-search"
                  @click="onSelectDesc(scope.row, scope.$index)"
                />
                <i
                  v-if="!isView"
                  v-show="scope.row.show_desc"
                  class="edac-icon action-icon edac-icon-copy_up"
                  @click="handleDescCopyUp(scope.row.descr)"
                />
                <i
                  v-if="isView && scope.row.desc_is_new"
                  v-show="scope.row.show_desc"
                  class="edac-icon action-icon edac-icon-add"
                  @click="onAddDesc(scope.row)"
                />
              </el-row>
              <el-row>
                <!--職員-->
                <div class="inline-block">
                  <i
                    :class="{
                      disable: !scope.row.show_staff,
                      'action-icon': action !== 'view',
                    }"
                    :title="$t('placeholder.staff')"
                    class="edac-icon edac-icon-office_clerk"
                    @click="handleSwitchIconDisable(scope, 'staff')"
                  />
                  <!--                  <el-input-->
                  <!--                    v-show="scope.row.show_staff"-->
                  <!--                    v-model="scope.row.st_name"-->
                  <!--                    :readonly="isView"-->
                  <!--                    :placeholder="$t('placeholder.staff')"-->
                  <!--                    :class="[{-->
                  <!--                      'is-error': scope.row.staff_is_error ? true : false,-->
                  <!--                      'link': isView && scope.row.st_code,-->
                  <!--                      'none': isView && !scope.row.st_code,-->
                  <!--                    }]"-->
                  <!--                    style="width:60px"-->
                  <!--                    @blur="onStaffBlur(scope.row, scope.$index)"-->
                  <!--                    @click.native="onStaffClick(scope.row, scope.$index)"-->
                  <!--                  />-->
                  <el-select
                    v-show="scope.row.show_staff"
                    v-model="scope.row.st_name"
                    :readonly="isView"
                    :disabled="isView"
                    :multiple="false"
                    :filterable="true"
                    :remote="true"
                    :remote-method="staffRemoteMethod"
                    :loading="scope.row.staffLoading"
                    :allow-create="false"
                    :default-first-option="false"
                    :clearable="true"
                    :placeholder="$t('placeholder.staff')"
                    :title="scope.row.st_name || $t('placeholder.staff')"
                    reserve-keyword
                    style="width: 70px"
                    @focus="onFocus(scope)"
                    @change="onChangeStaff(scope)"
                  >
                    <el-option
                      v-for="item in scope.row.staffList"
                      :key="item.staff_id"
                      :label="item[isEnglish ? 'st_name_en' : 'st_name_cn']"
                      :value="item.st_code"
                    />
                  </el-select>
                  <i
                    v-if="!isView"
                    v-show="scope.row.show_staff"
                    class="edac-icon action-icon edac-icon-search"
                    @click="onSelectStaff(scope.row, scope.$index)"
                  />
                </div>
                <!--參考號-->
                <div class="inline-block">
                  <i
                    :class="{
                      disable: !scope.row.show_ref,
                      'action-icon': action !== 'view',
                    }"
                    :title="$t('placeholder.ref')"
                    class="edac-icon edac-icon-reference"
                    @click="handleSwitchIconDisable(scope, 'ref')"
                  />
                  <el-input
                    v-show="scope.row.show_ref"
                    v-model="scope.row.ref"
                    :readonly="isView"
                    :placeholder="$t('placeholder.ref')"
                    :title="scope.row.ref || $t('placeholder.ref')"
                    style="width: 170px"
                  />
                </div>
                <!--預算-->
                <br v-if="scope.row.show_budget">
                <div
                  class="inline-block"
                  :style="{ width: scope.row.show_budget ? '100%' : 'auto' }"
                >
                  <i
                    :class="{
                      disable: !scope.row.show_budget,
                      'action-icon': action !== 'view',
                    }"
                    :title="$t('placeholder.budget')"
                    class="edac-icon edac-icon-budget"
                    @click="handleSwitchIconDisable(scope, 'budget')"
                  />
                  <!--                  <el-input-->
                  <!--                    v-show="scope.row.show_budget"-->
                  <!--                    v-model="scope.row.budget_code"-->
                  <!--                    :readonly="isView"-->
                  <!--                    :placeholder="$t('placeholder.budget')"-->
                  <!--                    style="width:60px"/>-->
                  <el-select
                    v-show="scope.row.show_budget"
                    v-model="scope.row.budget_code"
                    :readonly="isView"
                    :disabled="isView"
                    :multiple="false"
                    :filterable="true"
                    :remote="true"
                    :remote-method="budgetRemoteMethod"
                    :loading="scope.row.budgetLoading"
                    :allow-create="false"
                    :default-first-option="false"
                    :clearable="true"
                    :placeholder="$t('placeholder.budget')"
                    :title="scope.row.budget_code || $t('placeholder.budget')"
                    reserve-keyword
                    style="width: 70px"
                    @focus="onFocus(scope)"
                    @change="onChangeBudget(scope)"
                  >
                    <el-option
                      v-for="item in scope.row.budgetList"
                      :key="item.budget_id"
                      :label="item.budget_code"
                      :value="item.budget_code"
                    >
                      {{ `[${item.budget_code}] ${item[isEnglish ? 'name_en' : 'name_cn']}` }}
                    </el-option>
                  </el-select>
                  <i
                    v-if="!isView"
                    v-show="scope.row.show_budget"
                    class="edac-icon action-icon edac-icon-search"
                    @click="onSelectBudget(scope.row, scope.$index)"
                  />
                  <el-input
                    v-show="scope.row.show_budget"
                    :value="scope.row[language === 'en' ? 'budget_name_en' : 'budget_name_cn']"
                    :placeholder="$t('placeholder.budget')"
                    :title="
                      scope.row[language === 'en' ? 'budget_name_en' : 'budget_name_cn'] ||
                        $t('placeholder.budget')
                    "
                    readonly
                    style="width: 189px; width: calc(100% - 40px - 80px)"
                  />
                </div>
                <br v-if="scope.row.show_budget">
                <!--報價-->
                <div class="inline-block">
                  <i
                    :class="{
                      disable: !scope.row.show_quote,
                      'action-icon': action !== 'view',
                    }"
                    :title="$t('placeholder.quote')"
                    class="edac-icon edac-icon-tq"
                    @click="handleSwitchIconDisable(scope, 'quote')"
                  />
                  <el-select
                    v-show="scope.row.show_quote"
                    v-model="scope.row.vc_quote"
                    :disabled="isView"
                    style="width: 92px"
                  >
                    <el-option
                      v-for="item in TQOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-select
                    v-show="scope.row.show_quote"
                    v-if="scope.row.vc_quote === 'Q'"
                    v-model="scope.row.vc_qnum"
                    :disabled="isView"
                    :title="scope.row.vc_qnum || $t('placeholder.quote')"
                    style="width: 52px; margin-right: 30px"
                    placeholder="N"
                  >
                    <el-option v-for="i in 4" :key="i" :label="i - 1" :value="i - 1" />
                  </el-select>
                  <!--                  <i-->
                  <!--                    v-show="scope.row.show_quote"-->
                  <!--                    class="edac-icon action-icon edac-icon-accessory"-->
                  <!--                  />-->
                </div>
                <!--部門-->
                <!--<br v-if="scope.row.show_dept">-->
                <div class="inline-block">
                  <i
                    :class="{
                      disable: !scope.row.show_dept,
                      'action-icon': action !== 'view',
                    }"
                    :title="$t('placeholder.dept')"
                    class="edac-icon edac-icon-dept"
                    @click="handleSwitchIconDisable(scope, 'dept')"
                  />
                  <!--                  <el-input-->
                  <!--                    v-show="scope.row.show_dept"-->
                  <!--                    v-model="scope.row.dept_name"-->
                  <!--                    :readonly="isView"-->
                  <!--                    :placeholder="$t('placeholder.dept')"-->
                  <!--                    :class="[{-->
                  <!--                      'is-error': scope.row.dept_is_error ? true : false,-->
                  <!--                      'link': isView && scope.row.vc_dept,-->
                  <!--                      'none': isView && !scope.row.vc_dept,-->
                  <!--                    }]"-->
                  <!--                    style="width:60px"-->
                  <!--                    @blur="onDeptBlur(scope.row, scope.$index)"-->
                  <!--                    @click.native="onDeptClick(scope.row, scope.$index)"-->
                  <!--                  />-->

                  <el-select
                    v-show="scope.row.show_dept"
                    v-model="scope.row.dept_name"
                    :readonly="isView"
                    :disabled="isView"
                    :multiple="false"
                    :filterable="true"
                    :remote="true"
                    :remote-method="deptRemoteMethod"
                    :loading="scope.row.deptLoading"
                    :allow-create="false"
                    :default-first-option="false"
                    :clearable="true"
                    :placeholder="$t('placeholder.dept')"
                    :title="scope.row.dept_name || $t('placeholder.dept')"
                    reserve-keyword
                    style="width: 170px"
                    @focus="onFocus(scope)"
                    @change="onChangeDept(scope)"
                  >
                    <el-option
                      v-for="item in scope.row.deptList"
                      :key="item.department_id"
                      :label="item[isEnglish ? 'dept_name_en' : 'dept_name_cn']"
                      :value="item.dept_code"
                    />
                  </el-select>
                  <i
                    v-if="!isView"
                    v-show="scope.row.show_dept"
                    class="edac-icon action-icon edac-icon-search"
                    @click="onSelectDept(scope.row, scope.$index)"
                  />
                </div>
                <br v-if="scope.row.show_dept && scope.row.show_contra">
                <!--對沖編號-->
                <div class="inline-block">
                  <i
                    :class="{
                      disable: !scope.row.show_contra,
                      'action-icon': action !== 'view',
                    }"
                    :title="$t('placeholder.contra')"
                    class="edac-icon edac-icon-hedging"
                    @click="handleSwitchIconDisable(scope, 'contra')"
                  />
                  <!--                  <el-input-->
                  <!--                    v-show="scope.row.show_contra"-->
                  <!--                    v-model="scope.row.contra_name"-->
                  <!--                    :readonly="isView"-->
                  <!--                    :placeholder="$t('placeholder.contra')"-->
                  <!--                    :class="[{-->
                  <!--                      'is-error': scope.row.contra_is_error ? true : false,-->
                  <!--                      'link': isView && scope.row.vc_contra,-->
                  <!--                      'none': isView && !scope.row.vc_contra,-->
                  <!--                    }]"-->
                  <!--                    style="width:60px"-->
                  <!--                    @blur="onContraBlur(scope.row, scope.$index)"-->
                  <!--                    @click.native="onContraClick(scope.row, scope.$index)"-->
                  <!--                  />-->
                  <el-select
                    v-show="scope.row.show_contra"
                    v-model="scope.row.contra_name"
                    :readonly="isView"
                    :disabled="isView"
                    :multiple="false"
                    :filterable="true"
                    :remote="true"
                    :remote-method="contraRemoteMethod"
                    :loading="scope.row.contraLoading"
                    :allow-create="false"
                    :default-first-option="false"
                    :clearable="true"
                    :placeholder="$t('placeholder.contra')"
                    :title="scope.row.contra_name || $t('placeholder.contra')"
                    reserve-keyword
                    style="width: 70px"
                    @focus="onFocus(scope)"
                    @change="onChangeContra(scope)"
                  >
                    <el-option
                      v-for="item in scope.row.contraList"
                      :key="item.contra_id"
                      :label="item[isEnglish ? 'contra_name_en' : 'contra_name_cn']"
                      :value="item.contra_code"
                    />
                  </el-select>
                  <i
                    v-if="!isView"
                    v-show="scope.row.show_contra"
                    class="edac-icon action-icon edac-icon-search"
                    @click="onSelectContra(scope.row, scope.$index)"
                  />
                </div>
              </el-row>
              <!--收付款人-->
              <el-row v-if="info.vc_extra === 'Y'">
                <i class="edac-icon edac-icon-buyer action-icon" />
                <el-input
                  v-model="scope.row.vc_payee"
                  :readonly="isView"
                  :placeholder="$t('placeholder.payee')"
                  :class="[
                    {
                      'is-new': scope.row.payee_is_new ? true : false,
                    },
                  ]"
                  style="width: 300px"
                  @blur="onPayeeBlur(scope.row, scope.$index)"
                />
                <i
                  v-if="!isView"
                  class="edac-icon action-icon edac-icon-search"
                  @click="onSelectPayee(scope.row, scope.$index)"
                />
              </el-row>
            </div>
          </template>
        </el-table-column>
      </slot>
      <el-table-column
        :label="$t('daily.label.amount')"
        :resizable="false"
        width="130"
        align="right"
        header-align="right"
      >
        <template v-if="scope && scope.row" slot-scope="scope">
          <ENumeric
            v-if="info.vt_category === 'P' || info.vt_category === 'C'"
            v-model="scope.row.amount_dr"
            :read-only="isView"
          />
          <ENumeric
            v-else-if="info.vt_category === 'R'"
            v-model="scope.row.amount_cr"
            :read-only="isView"
          />
          <div v-else style="display: inline-flex">
            <el-select
              v-model="scope.row.select_amount_type"
              :disabled="isView"
              style="width: 50px"
              @change="changeAmount(scope)"
            >
              <el-option label="Dr" value="Dr" />
              <el-option label="Cr" value="Cr" />
            </el-select>
            <ENumeric
              v-if="scope.row.select_amount_type === 'Dr'"
              v-model="scope.row.amount_dr"
              :read-only="isView"
              style="width: calc(100% - 50px)"
            />
            <ENumeric
              v-else
              v-model="scope.row.amount_cr"
              :read-only="isView"
              style="width: calc(100% - 50px)"
            />
          </div>
        </template>
      </el-table-column>

      <!--<el-table-column-->
      <!--  label=" "-->
      <!--  width="0"-->
      <!--  align="left"-->
      <!--/>-->
    </el-table>
    <!--選擇會計編號 彈窗-->
    <dialogAccount
      :dialog-visible.sync="dialogAccountVisible"
      :fy_code="fy_code"
      :default-fund-id="info.fund_id"
      @selectRow="handleAccountSelect"
    />

    <!--選擇收款人 彈窗-->
    <dialogPayee
      :dialog-visible.sync="dialogPayeeVisible"
      :fy_code="fy_code"
      @selectRow="handlePayeeSelect"
    />

    <!--選擇職員 彈窗-->
    <dialogStaff
      :dialog-visible.sync="dialogStaffVisible"
      :fy_code="fy_code"
      @selectRow="handleStaffSelect"
    />
    <!--選擇部門 彈窗-->
    <dialogDepartment
      :dialog-visible.sync="dialogDeptVisible"
      :fy_code="fy_code"
      @selectRow="handleDeptSelect"
    />
    <!--選擇對沖編號 彈窗-->
    <dialogContra
      :dialog-visible.sync="dialogContraVisible"
      :fy_code="fy_code"
      @selectRow="handleContraSelect"
    />
    <!--選擇描述 彈窗-->
    <dialogDescription
      :dialog-visible.sync="dialogDescVisible"
      :fy_code="fy_code"
      :fund-id="info.fund_id"
      @selectRow="handleDescSelect"
    />
    <!--選擇預算 彈窗-->
    <dialogBudget
      :dialog-visible.sync="dialogBudgetVisible"
      :fy_code="fy_code"
      @selectRow="handleBudgetSelect"
    />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import ENumeric from '@/components/ENumeric'

import dialogAccount from '@/views/daily/components/DialogAccount'
import dialogPayee from '@/views/daily/components/DialogPayee'
import dialogStaff from '@/views/daily/components/DialogStaff'
import dialogDepartment from '@/views/daily/components/DialogDepartment'
import dialogContra from '@/views/daily/components/DialogContra'
import dialogDescription from '@/views/daily/components/DialogDescription'
import dialogBudget from '@/views/daily/components/DialogBudget'

import { searchCompanies } from '@/api/assistance/payeePayer'
import { searchStaffs } from '@/api/assistance/staff'
import { getAccount, searchAccountByCode } from '@/api/master/account'
import { searchDepartment } from '@/api/assistance/department'
import { fetchContras } from '@/api/assistance/contra'
import { searchBudget } from '@/api/budget'
import { createDescription, fetchDescriptions } from '@/api/assistance/description'

export default {
  name: 'DTable',
  components: {
    VBreadCrumb,
    ENumeric,
    dialogAccount,
    dialogPayee,
    dialogStaff,
    dialogDepartment,
    dialogContra,
    dialogDescription,
    dialogBudget,
  },
  props: {
    isView: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default() {
        return []
      },
    },
    info: {
      type: Object,
      required: true,
    },
    fy_code: {
      type: [String, Object],
      required: true,
    },
    action: {
      type: String,
      default: 'add',
    },
    lg_id: {
      type: Number,
      default: 0,
    },
    detailStyles: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      // 行
      currentInputRow: null,
      currentRowItem: null,
      currentRowIndex: -1,
      // 選擇會計科目
      dialogAccountVisible: false,

      // 選擇收款人
      dialogPayeeVisible: false,

      // 選擇職員
      dialogStaffVisible: false,

      // 選擇部門
      dialogDeptVisible: false,

      // 選擇對沖編號
      dialogContraVisible: false,

      // 選擇傳票明細
      dialogDescVisible: false,

      // 選擇預算明細
      dialogBudgetVisible: false,

      loading: false,
      dialogLoading: false,

      // temp
      num: 0,

      tableData: this.formatData(this.data),
      defaultItem: {
        ac_code: '',
        account_id: '',
        descr: '',
        tx_type: 'I',
        amount_dr: '0.00',
        amount_cr: '0.00',
        vc_payee: '',
        ref: '',
        budget_code: null,
        st_code: null,
        vc_contra: null,
        contra_name: '',
        contra_name_en: '',
        contra_name_cn: '',
        vc_dept: null,
        dept_name: '',
        dept_name_en: '',
        dept_name_cn: '',
        vc_quote: '',
        vc_qnum: null,
        st_name: '',
        st_name_en: '',
        st_name_cn: '',
      },

      // 手動輸入驗證
      payeeIsNew: false,
      descIsNew: false,
      staffInvalid: false,
      deptInvalid: false,

      resetDescView: true,
    }
  },
  computed: {
    ...mapGetters(['language']),
    TQOptions() {
      return [
        {
          label: 'N/A',
          value: '',
        },
        {
          label: 'Quotation',
          value: 'Q',
        },
        {
          label: 'Tender',
          value: 'T',
        },
      ]
    },
  },
  watch: {
    tableTable: {
      deep: true,
      immediate: true,
      handler(value) {
        if (!value) return
        console.log('watch tableTable')
        this.$emit('update:data', value)
      },
    },
    data: {
      deep: true,
      immediate: true,
      handler(value) {
        console.log('watch data')
        this.tableData = this.formatData(value)
      },
    },
  },
  mounted() {},
  methods: {
    resetDescState() {
      this.resetDescView = false
      this.$nextTick(() => {
        this.resetDescView = true
      })
    },
    formatData(data) {
      data.forEach(item => {
        if (!item.select_amount_type) {
          this.$set(item, 'select_amount_type', Number(item.amount_dr) === 0 ? 'Cr' : 'Dr')
        }
        if (item.st_name == null) {
          if (item.st_name_en == null) {
            this.$set(item, 'st_name_en', '')
            this.$set(item, 'st_name_cn', '')
            this.$set(item, 'st_name', '')
          } else {
            this.$set(item, 'st_name', this.language === 'en' ? item.st_name_en : item.st_name_cn)
          }
        }
        if (item.dept_name == null) {
          if (item.dept_name_en == null) {
            this.$set(item, 'dept_name_en', '')
            this.$set(item, 'dept_name_cn', '')
            this.$set(item, 'dept_name', '')
          } else {
            this.$set(
              item,
              'dept_name',
              this.language === 'en' ? item.dept_name_en : item.dept_name_cn,
            )
          }
        }
        if (item.contra_name == null) {
          if (item.dept_name_en == null) {
            this.$set(item, 'contra_name_en', '')
            this.$set(item, 'contra_name_cn', '')
            this.$set(item, 'contra_name', '')
          } else {
            this.$set(
              item,
              'contra_name',
              this.language === 'en' ? item.contra_name_en : item.contra_name_cn,
            )
          }
        }

        this.$set(item, 'tx_type_has_E', item.ac_E === 'Y')
        this.$set(item, 'tx_type_has_I', item.ac_I === 'Y')
        // if (!item.hasOwnProperty('tx_type_has_E')) {
        //   this.$set(item, 'tx_type_has_E', true)
        // }
        // if (!item.hasOwnProperty('tx_type_has_I')) {
        //   this.$set(item, 'tx_type_has_I', true)
        // }
        // this.$set(item, 'tx_type_has_E', true)
        // this.$set(item, 'tx_type_has_I', true)
        // this.$set(item, 'tx_type_has_E', data.ac_E === 'Y')
        // this.$set(item, 'tx_type_has_I', data.ac_I === 'Y')
        // if (this.action !== 'add') {
        //   this.$set(item, 'show_desc', false)
        //   this.$set(item, 'show_staff', false)
        //   this.$set(item, 'show_dept', false)
        //   this.$set(item, 'show_contra', false)
        //   this.$set(item, 'show_ref', false)
        // } else {
        //   this.$set(item, 'show_desc', true)
        //   this.$set(item, 'show_staff', true)
        //   this.$set(item, 'show_dept', true)
        //   this.$set(item, 'show_contra', true)
        //   this.$set(item, 'show_ref', true)
        // }
        if (!item.hasOwnProperty('show_desc')) {
          // item.show_dept =
          //     item.show_staff =
          //       item.show_contra =
          //         item.show_ref =
          //           item.show_budget =
          //             item.show_quote =
          //               this.action !== 'add'
          if (this.action !== 'add') {
            item.show_dept = !!item.dept_code
            item.show_staff = !!item.st_code
            item.show_contra = !!item.vc_contra
            item.show_ref = !!item.ref
            item.show_budget = !!item.budget_code
            item.show_quote = !!item.vc_quote
          }
          item.show_desc = true
          // item.show_staff = true
        }
        if (this.action === 'add') {
          // item.show_staff = this.show_staff
          // item.show_ref = this.show_ref
          // item.show_budget = this.show_budget
          // item.show_quote = this.show_quote
          // item.show_dept = this.show_dept
          // item.show_contra = this.show_contra
          // const keys = Object.keys(this.detailStyles)
          // for (let i = 0; i < keys.length; i++) {
          //   const key = keys[i]
          //   item[key] = this.detailStyles[key]
          // }
        }
      })
      return data
    },
    isStripe({ row, rowIndex }) {
      if (this.lg_id && this.lg_id === row.lg_id) {
        return 'selected-row'
      } else {
        return ''
      }
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    // 會計
    onSelectAccount(data, index) {
      if (this.isView) return
      // console.log('onSelectAccount', index, data)
      this.dialogAccountVisible = true
      this.currentRowItem = data
      this.currentRowIndex = index
    },
    handleAccountSelect(data, fund_id) {
      // Todo 按會計科目選IE
      this.$set(this.currentRowItem, 'ac_code', data.ac_code)
      this.$set(this.currentRowItem, 'ac_name_cn', data.ac_name_cn)
      this.$set(this.currentRowItem, 'ac_name_en', data.ac_name_en)
      this.$set(this.currentRowItem, 'account_id', data.account_id)
      if (data.ac_E === 'Y') {
        this.$set(this.currentRowItem, 'tx_type', 'E')
        this.$set(this.currentRowItem, 'tx_type_disable', false)
      } else if (data.ac_I === 'Y') {
        this.$set(this.currentRowItem, 'tx_type', 'I')
        this.$set(this.currentRowItem, 'tx_type_disable', false)
      } else {
        this.$set(this.currentRowItem, 'tx_type', '')
        this.$set(this.currentRowItem, 'tx_type_disable', true)
      }
      this.$set(this.currentRowItem, 'ac_E', data.ac_E)
      this.$set(this.currentRowItem, 'ac_I', data.ac_I)
      this.$set(this.currentRowItem, 'tx_type_has_E', data.ac_E === 'Y')
      this.$set(this.currentRowItem, 'tx_type_has_I', data.ac_I === 'Y')
      this.$set(this.currentRowItem, 'ac_is_error', false)
      this.handleReloadGrantEnquiryPage(fund_id, data.ac_code, this.info.fy_code, this.info.pd_code)
    },
    // 職員
    onSelectStaff(data, index) {
      if (this.isView) return
      // console.log('onSelectStaff', index, data)
      this.dialogStaffVisible = true
      this.currentRowItem = data
      this.currentRowIndex = index
    },
    handleStaffSelect(
      data = {
        st_code: '',
        st_name_en: '',
        st_name_cn: '',
        st_name: '',
        staff_is_error: false,
      },
    ) {
      if (data) {
        this.$set(this.currentRowItem, 'st_code', data.st_code)
        this.$set(this.currentRowItem, 'st_name_en', data.st_name_en)
        this.$set(this.currentRowItem, 'st_name_cn', data.st_name_cn)
        this.$set(
          this.currentRowItem,
          'st_name',
          this.language === 'en' ? data.st_name_en : data.st_name_cn,
        )

        this.$set(this.currentRowItem, 'staff_is_error', false)
        this.handleReloadStaffEnquiryPage(data.st_code)
      }
    },
    // 部門
    onSelectDept(data, index) {
      if (this.isView) return
      // console.log('onSelectStaff', index, data)
      this.dialogDeptVisible = true
      this.currentRowItem = data
      this.currentRowIndex = index
    },
    handleDeptSelect(
      data = {
        vc_dept: '',
        dept_name_en: '',
        dept_name_cn: '',
        dept_name: '',
        dept_is_error: false,
      },
    ) {
      // console.log('handleDeptSelect', data)
      if (data) {
        this.$set(this.currentRowItem, 'vc_dept', data.dept_code)
        this.$set(this.currentRowItem, 'dept_name_en', data.dept_name_en)
        this.$set(this.currentRowItem, 'dept_name_cn', data.dept_name_cn)
        this.$set(
          this.currentRowItem,
          'dept_name',
          this.language === 'en' ? data.dept_name_en : data.dept_name_cn,
        )
        this.$set(this.currentRowItem, 'dept_is_error', false)
        this.handleReloadDeptEnquiryPage(data.dept_code)
      }
    },
    // 收款人
    onSelectPayee(data, index) {
      if (this.isView) return
      // console.log('onSelectPayee', index, data)
      this.dialogPayeeVisible = true
      this.currentRowItem = data
      this.currentRowIndex = index
    },
    handlePayeeSelect(
      data = {
        vc_payee: '',
        payee_is_new: false,
      },
    ) {
      // console.log('handlePayeeSelect', data)
      if (data && data.comp_name) {
        this.$set(this.currentRowItem, 'vc_payee', data.comp_name)
        this.$set(this.currentRowItem, 'payee_is_new', false)
      }
    },
    // 對沖編號
    onSelectContra(data, index) {
      if (this.isView) return
      // console.log('onSelectPayee', index, data)
      this.dialogContraVisible = true
      this.currentRowItem = data
      this.currentRowIndex = index
    },
    handleContraSelect(
      data = {
        vc_contra: '',
        contra_name_en: '',
        contra_name_cn: '',
        contra_name: false,
      },
    ) {
      if (data) {
        this.$set(this.currentRowItem, 'vc_contra', data.contra_code)
        this.$set(this.currentRowItem, 'contra_name_en', data.contra_name_en)
        this.$set(this.currentRowItem, 'contra_name_cn', data.contra_name_cn)
        this.$set(
          this.currentRowItem,
          'contra_name',
          this.language === 'en' ? data.contra_name_en : data.contra_name_cn,
        )
        this.$set(this.currentRowItem, 'contra_is_error', false)
        this.handleReloadContraEnquiryPage(data.contra_code, data.contra_id)
      }
    },
    // 描述
    onSelectDesc(data, index) {
      if (this.isView) return
      this.dialogDescVisible = true
      this.currentRowItem = data
      this.currentRowIndex = index
    },
    handleDescSelect(data) {
      if (data) {
        this.$set(this.currentRowItem, 'descr', data.desc)
        this.$set(this.currentRowItem, 'desc_is_new', false)
      }
    },
    handleDescCopyUp(desc) {
      if (this.isView) return
      this.$emit('handleDescCopyUp', desc)
    },
    // 預算管理
    onSelectBudget(data, index) {
      if (this.isView) return
      this.currentRowItem = data
      this.currentRowIndex = index
      this.dialogBudgetVisible = true
    },
    handleBudgetSelect(
      data = {
        budget_code: '',
        budget_name_cn: '',
        budget_name_en: '',
      },
    ) {
      if (data) {
        this.$set(this.currentRowItem, 'budget_code', data.budget_code)
        this.$set(this.currentRowItem, 'budget_name_cn', data.name_cn)
        this.$set(this.currentRowItem, 'budget_name_en', data.name_en)
      }
    },
    addRow() {
      if (this.isView) return
      const newRow = Object.assign({}, this.defaultItem)
      if (this.info.vc_summary) {
        newRow.descr = this.info.vc_summary
      }
      let checked = -1
      for (let i = 0; i < this.tableData.length; i++) {
        const item = this.tableData[i]
        console.log(i, item._checked, item.descr)
        if (item.hasOwnProperty('_checked') && item._checked) {
          checked = i
          break
        }
      }
      if (checked === -1) {
        this.tableData.push(newRow)
      } else {
        this.tableData.splice(checked, 0, newRow)
      }
      for (let i = this.tableData.length - 1; i >= 0; i--) {
        this.$set(this.tableData[i], '_checked', false)
      }
      this.$forceUpdate()
    },
    deleteRow() {
      if (this.isView) return
      const data = this.tableData
      if (data.some(i => i._checked)) {
        for (let i = data.length - 1; i >= 0; i--) {
          const item = data[i]
          if (item.hasOwnProperty('_checked') && item._checked) {
            data.splice(i, 1)
          } else {
            this.$set(data[i], '_checked', false)
          }
        }
      } else {
        data.pop()
      }
      // this.$refs.DTable.clearSelection()
      this.tableData = data
      if (data.length === 0) {
        this.addRow()
      }
      // if (this.tableData.length < 2) return
      // this.tableData.pop()
    },
    changeAmount({ row }) {
      if (row.select_amount_type === 'Dr') {
        row.amount_dr = row.amount_cr
        row.amount_cr = 0
      } else {
        row.amount_cr = row.amount_dr
        row.amount_dr = 0
      }
    },
    onHeaderDragend(newWidth, oldWidth, column, event) {
      // if (newWidth < column.minWidth) {
      //   newWidth = column.minWidth
      //   debugger
      // }
    },
    onAcCodeBlur(row, index) {
      if (this.isView) return
      let ac_is_error = true
      const ac_code = row.ac_code
      if (ac_code && typeof ac_code === 'string' && ac_code.trim().length) {
        const fy_code = this.fy_code
        searchAccountByCode({
          fy_code,
          ac_code,
        })
          .then(res => {
            console.log(res)
            if (typeof res === 'object' && res.ac_code) {
              this.currentRowItem = row
              this.currentRowIndex = index
              this.handleAccountSelect(res, res.f_parent_fund_id) // 賦值row，及 ac_is_error
              ac_is_error = false
            } else {
              // eslint-disable-next-line no-restricted-syntax
              this.$set(row, 'ac_name_cn', '編號無效!')
              this.$set(row, 'ac_name_en', 'Invalid Code!')
              return Promise.reject()
            }
          })
          .catch(() => {
            // 錯誤才需要賦值，
            this.$set(row, 'ac_is_error', ac_is_error)
            this.currentRowItem = row
            this.handleAccountSelect(
              {
                ac_code: null,
                ac_name_cn: null,
                ac_name_en: null,
                account_id: null,
              },
              '',
            ) // 賦值row，及 ac_is_error
          })
      } else {
        this.$set(row, 'ac_is_error', ac_is_error)
      }
    },
    onStaffBlur(row, index) {
      if (this.isView) return
      const staff_is_error = true
      const st_name = row.st_name || ''
      if (st_name && typeof st_name === 'string' && st_name.trim().length) {
        const fy_code = this.fy_code
        searchStaffs({ fy_code, name: st_name })
          .then(res => {
            if (res.length) {
              const data = res[0]
              this.currentRowItem = row
              this.currentRowIndex = index
              this.handleStaffSelect(data) // 賦值row，及 ac_is_error
            } else {
              this.$set(row, 'st_name_cn', this.$t('error.invalidStaff'))
              this.$set(row, 'st_name_en', 'Invalid Code!')
              return Promise.reject()
            }
          })
          .catch(() => {
            // 錯誤才需要賦值，
            this.$set(row, 'staff_is_error', staff_is_error)
          })
      } else if (typeof st_name === 'string' && st_name.length === 0) {
        this.$set(row, 'staff_is_error', false) // 空 正常
        this.currentRowItem = row
        this.handleStaffSelect({
          st_code: null,
          st_name_en: null,
          st_name_cn: null,
        }) // 賦值row，及 ac_is_error
      } else {
        this.$set(row, 'staff_is_error', true) // 非空 錯誤
      }
    },
    onDeptBlur(row, index) {
      if (this.isView) return
      const dept_name = row.dept_name || ''
      if (dept_name && typeof dept_name === 'string' && dept_name.trim().length) {
        const fy_code = this.fy_code
        searchDepartment({ fy_code, name: dept_name })
          .then(res => {
            if (res.length) {
              const data = res[0]
              this.currentRowItem = row
              this.currentRowIndex = index
              this.handleDeptSelect(data) // 賦值row，及 dept_is_error
            } else {
              this.$set(row, 'dept_name_cn', this.$t('error.invalidDept'))
              this.$set(row, 'dept_name_en', 'Invalid Code!')
              return Promise.reject()
            }
          })
          .catch(() => {
            // 錯誤才需要賦值，
            this.$set(row, 'dept_is_error', true) // 返回空 錯誤
          })
      } else if (typeof dept_name === 'string' && dept_name.length === 0) {
        this.$set(row, 'dept_is_error', false) // 空 正常
        this.currentRowItem = row
        this.handleDeptSelect({
          dept_code: null,
          dept_name_en: null,
          dept_name_cn: null,
        }) // 賦值row，及 dept_is_error
      } else {
        this.$set(row, 'dept_is_error', true) // 非空 錯誤
      }
    },
    onContraBlur(row, index) {
      if (this.isView) return
      const contra_name = row.contra_name || ''
      if (contra_name && typeof contra_name === 'string' && contra_name.trim().length) {
        const fy_code = this.fy_code
        fetchContras({ fy_code, name: contra_name })
          .then(res => {
            if (res.length) {
              const data = res[0]
              this.currentRowItem = row
              this.currentRowIndex = index
              this.handleContraSelect(data) // 賦值row，及 contra_is_error
            } else {
              this.$set(row, 'contra_name_cn', this.$t('error.invalidContra'))
              this.$set(row, 'contra_name_en', 'Invalid Code!')
              return Promise.reject()
            }
          })
          .catch(() => {
            // 錯誤才需要賦值，
            this.$set(row, 'contra_is_error', true) // 返回空 錯誤
          })
      } else if (typeof contra_name === 'string' && contra_name.length === 0) {
        this.$set(row, 'contra_is_error', false) // 空 正常
        this.currentRowItem = row
        this.handleContraSelect({
          contra_code: null,
          contra_name_en: null,
          contra_name_cn: null,
        }) // 賦值row，及 contra_is_error
      } else {
        this.$set(row, 'contra_is_error', true) // 非空 錯誤
      }
    },
    onPayeeBlur(row, index) {
      if (this.isView) return
      const payee_name = row.vc_payee
      let is_new = false
      if (typeof payee_name === 'string' && payee_name.trim().length) {
        const fy_code = this.fy_code
        searchCompanies({ fy_code, name: payee_name })
          .then(res => {
            if (res.length === 0) {
              is_new = true
            }
          })
          .finally(() => {
            this.$set(row, 'payee_is_new', is_new)
          })
      } else {
        this.$set(row, 'payee_is_new', is_new)
      }
    },
    onDescBlur(row, index) {
      if (this.isView) return
      const desc = row.descr
      let is_new = false
      if (typeof desc === 'string' && desc.trim().length) {
        const fy_code = this.fy_code
        fetchDescriptions({ fy_code, full_desc: desc })
          .then(res => {
            if (res.length === 0) {
              is_new = true
            }
          })
          .finally(() => {
            this.$set(row, 'desc_is_new', is_new)
          })
      } else {
        this.$set(row, 'desc_is_new', is_new)
      }
    },
    onAcCodeClick(data, index) {
      if (this.isView && data.ac_code) {
        const ac_code = data.ac_code
        const fy_code = this.fy_code
        searchAccountByCode({ ac_code, fy_code }).then(res => {
          if (typeof res === 'object' && res.ac_code) {
            this.currentRowItem = data
            this.currentRowIndex = index
            this.handleAccountSelect(res, res.f_parent_fund_id) // 賦值row，及 ac_is_error
          }
        })
      }
    },
    onStaffClick(data, index) {
      if (this.isView && data.st_code) {
        const st_code = data.st_code
        this.handleReloadStaffEnquiryPage(st_code)
      }
    },
    onDeptClick(data, index) {
      if (this.isView && data.vc_dept) {
        const dept_code = data.vc_dept
        this.handleReloadDeptEnquiryPage(dept_code)
      }
    },
    onContraClick(data, index) {
      if (this.isView && data.vc_contra) {
        const contra_code = data.vc_contra
        this.handleReloadContraEnquiryPage(contra_code)
      }
    },
    handleSwitchIconDisable(scope, key) {
      console.log('handleSwitchIconDisable - 1')
      // if (this.action !== 'view') {
      const iKey = 'show_' + key
      console.log('handleSwitchIconDisable - 2', scope.row[iKey])
      this.$set(scope.row, iKey, !scope.row[iKey])
      console.log('handleSwitchIconDisable -3', scope.row[iKey])
      // }
      this.$forceUpdate()
      this.resetDescState()
    },
    /* ---------------- 组件通信 ---------------- */
    handleReloadStaffEnquiryPage(staffCode) {
      if (!staffCode) return
      this.$bus.emit('enquiryStaffFetch', staffCode)
    },
    handleReloadDeptEnquiryPage(deptCode) {
      if (!deptCode) return
      this.$bus.emit('enquiryDeptFetch', deptCode)
    },
    handleReloadContraEnquiryPage(contraCode, contra_id) {
      if (!contraCode) return
      this.$bus.emit('enquiryContraFetch', contraCode, contra_id)
    },
    handleReloadGrantEnquiryPage(fund_id, ac_code, fy_code, pd_code) {
      if (!fund_id || !ac_code) return
      this.$bus.emit('enquiryGrantFetch', fund_id, ac_code, null, fy_code, pd_code)
    },
    onFocus(scope) {
      // this.currentInputRow = scope.row

      this.currentRowItem = scope.row
      this.currentRowIndex = scope.$index
    },
    // 職員
    onChangeStaff(scope) {
      this.currentRowItem = scope.row
      this.currentRowIndex = scope.$index
      if (scope.row && scope.row.staffList && Array.isArray(scope.row.staffList)) {
        const staff = scope.row.staffList.find(s => s.st_code === scope.row.st_name)
        if (staff) {
          this.handleStaffSelect(staff)
          return
        }
      }
      this.handleStaffSelect()
    },
    async staffRemoteMethod(query) {
      const row = this.currentRowItem
      const fy_code = this.info.fy_code
      if (!row || !fy_code || query.length === 0) return false
      this.$set(row, 'staffLoading', true)

      try {
        row.staffList = await searchStaffs({ fy_code, name: query })
      } catch (e) {
        console.error(e)
      }
      this.$set(row, 'staffLoading', false)
    },
    // 部門
    onChangeDept(scope) {
      this.currentRowItem = scope.row
      this.currentRowIndex = scope.$index
      if (scope.row && scope.row.deptList && Array.isArray(scope.row.deptList)) {
        const dept = scope.row.deptList.find(s => s.dept_code === scope.row.dept_name)
        if (dept) {
          this.handleDeptSelect(dept)
          return
        }
      }
      this.handleDeptSelect()
    },
    async deptRemoteMethod(query) {
      const row = this.currentRowItem
      const fy_code = this.info.fy_code
      if (!row || !fy_code || query.length === 0) return false
      this.$set(row, 'deptLoading', true)

      try {
        row.deptList = await searchDepartment({ fy_code, name: query })
      } catch (e) {
        console.error(e)
      }
      this.$set(row, 'deptLoading', false)
    },
    // 對沖編號
    onChangeContra(scope) {
      this.currentRowItem = scope.row
      this.currentRowIndex = scope.$index
      if (scope.row && scope.row.contraList && Array.isArray(scope.row.contraList)) {
        const contra = scope.row.contraList.find(s => s.contra_code === scope.row.contra_name)
        if (contra) {
          this.handleContraSelect(contra)
          return
        }
      }
      this.handleContraSelect()
    },
    async contraRemoteMethod(query) {
      const row = this.currentRowItem
      const fy_code = this.info.fy_code
      if (!row || !fy_code || query.length === 0) return false
      this.$set(row, 'contraLoading', true)
      try {
        row.contraList = await fetchContras({ fy_code, name: query })
      } catch (e) {
        console.error(e)
      }
      this.$set(row, 'contraLoading', false)
    },
    // 預算
    onChangeBudget(scope) {
      this.currentRowItem = scope.row
      this.currentRowIndex = scope.$index
      if (scope.row && scope.row.budgetList && Array.isArray(scope.row.budgetList)) {
        const budget = scope.row.budgetList.find(s => s.budget_code === scope.row.budget_code)
        if (budget) {
          this.handleBudgetSelect(budget)
          return
        }
      }
      this.handleBudgetSelect()
    },
    async budgetRemoteMethod(query) {
      const row = this.currentRowItem
      const fy_code = this.info.fy_code
      if (!row || !fy_code || query.length === 0) return false
      this.$set(row, 'budgetLoading', true)
      try {
        row.budgetList = await searchBudget({ fy_code, budget_name: query })
      } catch (e) {
        console.error(e)
      }
      this.$set(row, 'budgetLoading', false)
    },
    async onAddDesc(item) {
      console.log('onAddDesc', item)
      if (item.descr.replace(/ /g, '').length === 0) {
        this.$message.error(this.$t('daily.voucher.message.descCannotEmpty'))
        return
      }
      try {
        const multi_ac_code = item.ac_code
        const account_id = item.account_id
        const account = await getAccount(account_id)
        const active_year = account.active_year
        const desc = item.descr
        const BIEArr = ['ac_B', 'ac_I', 'ac_E']
          .filter(i => account[i] === 'Y')
          .map(i => i.charAt(i.length - 1))
        const BIE = BIEArr.join(',')
        const res = await createDescription({ multi_ac_code, active_year, desc, BIE })
        if (res) {
          console.log(res)
          this.$set(item, 'desc_is_new', false)
          this.$message.success(this.$t('message.success'))
        }
      } catch (e) {
        console.error(e)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
i {
  font-size: 16px;
  vertical-align: middle;
  line-height: 25px;
}
.edac-icon {
  color: #68afff;
  &.disable {
    color: #b9b6b6;
    cursor: pointer;
  }
}
i.edac-icon.action.edac-icon-search {
  margin-left: -10px;
}
.d-table {
  .edac-icon.action {
    cursor: pointer;
  }
  &.is-view {
    .edac-icon.action {
      cursor: not-allowed;
    }
  }
  .td-account {
    margin: 3px 0;
  }
  .td-desc {
    margin: 3px 0;
    /*min-width: 350px;*/
    /deep/ {
      .inline-block {
        display: inline-block;
      }

      .desc-row {
        display: flex;
        .desc-input {
          flex-grow: 1;
          padding: 0 5px;
        }
        .edac-icon-add {
          width: 18px;
          height: 18px;
          background: #409eff;
          color: #ffffff;
          text-align: center;
          border-radius: 50%;
          line-height: 18px;
          vertical-align: middle;
        }
      }
    }
  }
  /deep/ {
    td > .cell {
      padding: 2px 5px;
    }
    .selected-row {
      background-color: #e6f4ff;
    }
    input {
      padding: 0px 1px;
      border: none;
      border-radius: 0;
      border-bottom: 1px solid #dcdfe6;
      background: transparent;
    }
    .el-input.is-disabled .el-input__inner {
      background-color: transparent;
      color: inherit;
    }
    .el-form-item__error {
      display: none;
    }
    .el-input.is-error input {
      color: red;
    }
    .el-input.is-new input {
      color: #169a55;
    }
    .link input {
      cursor: pointer;
    }
    .none input {
      cursor: not-allowed;
    }

    td.check-box {
      .cell {
        text-overflow: unset;
        padding: 0 5px;
      }
    }
  }
}
</style>
