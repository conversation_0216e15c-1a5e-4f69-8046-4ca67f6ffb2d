<script>
import { mapGetters } from 'vuex'
import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt, insertData } from '@/utils/pdf/index'

import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import { chequeConvertCN, chequeConvertEnglish } from '@/utils/amount'
import { getEmptyImageBase64 } from '@/utils/pdf'
import { getVoucherType } from '@/api/master/voucherType'
export default {
  name: 'HandlePDF',
  mixins: [loadPrintoutSetting],
  computed: {
    ...mapGetters(['school', 'remoteServerInfo']),
  },
  data() {
    return {
      logoCache: null,
    }
  },
  methods: {
    async onPrint(isPreview = false) {
      if (this.printing) {
        return
      }
      if (!isInitPDF) {
        return initTips()
      }
      if (this.multipleSelection.length === 0) {
        this.$message.error(this.$t('daily.cheque.message.selectCheque'))
        return
      }
      this.printing = true
      try {
        const docs = []
        for (const cheque of this.multipleSelection) {
          docs.push(
            await this.handlerPrintCheque(
              {
                vc_payee: cheque.vc_payee,
                vc_date: cheque.vc_chq_date,
                vc_amount: cheque.vc_amount.replace(/,/g, ''),
              },
              cheque.chq_template_code,
              isPreview,
            ),
          )
        }
        await openPdf(docs)
      } catch (e) {
        console.error(e)
      }
      this.printing = false
    },
    // ****************    支票           ********************
    async handlerPrintCheque(voucher, chq_template_code, isPreview) {
      const t_ps_code = 'pdfcheque'
      let printSetting
      try {
        printSetting = await this.loadPrintoutSetting(t_ps_code, null, chq_template_code)
      } catch (e) {
        this.$message.error(this.$t('message.printSettingEmpty'))
        return
      }

      const isHideAC =
        voucher.vt_category === 'P' &&
        voucher.vc_payee
          .toLowerCase()
          .split(' ')
          .some(i => i === 'cash')
      const data = await this.generateCheque({
        payee: voucher.vc_payee,
        date: voucher.vc_date,
        amount: voucher.vc_amount,
        chq_template_code,
        printSetting,
        isPreview,
        isHideAC,
      })

      const title = this.$t('setting.printout.navMenu.daily_cheque', printSetting.language)
      const doc = await this.generatePDFCheque({
        title,
        data,
        printSetting,
      })
      return doc
    },

    async generateCheque({
      payee,
      date,
      amount,
      chq_template_code,
      printSetting,
      isPreview,
      isHideAC,
    }) {
      console.log('generateCheque', {
        payee,
        date,
        amount,
        chq_template_code,
        printSetting,
        isPreview,
        isHideAC,
      })
      const showUnderline = chq_template_code && !chq_template_code.includes('_')
      const showChequeStub = chq_template_code && chq_template_code.includes('-')
      const language = printSetting.language
      const cheque_width = mm2pt(printSetting.cheque_width)
      const cheque_height = mm2pt(printSetting.cheque_height)
      const offset_x = mm2pt(printSetting.offset_x)
      const offset_y = mm2pt(printSetting.offset_y)
      const amount_x = mm2pt(printSetting.amount_x) + offset_x
      const amount_y = mm2pt(printSetting.amount_y) + offset_y
      const date_x = mm2pt(printSetting.date_x) + offset_x
      const date_y = mm2pt(printSetting.date_y) + offset_y
      const payee_x = mm2pt(printSetting.payee_x) + offset_x
      const payee_y = mm2pt(printSetting.payee_y) + offset_y
      const text_amount_x = mm2pt(printSetting.text_amount_x) + offset_x
      const text_amount_y = mm2pt(printSetting.text_amount_y) + offset_y
      const ac_payee_only_x = mm2pt(printSetting.ac_payee_only_x) + offset_x
      const ac_payee_only_y = mm2pt(printSetting.ac_payee_only_y) + offset_y
      const crossed_bearer_x = mm2pt(printSetting.crossed_bearer_x) + offset_x
      const crossed_bearer_y = mm2pt(printSetting.crossed_bearer_y) + offset_y
      // 支票頭
      const stub_date_x = mm2pt(printSetting.stub_date_x) + offset_x
      const stub_date_y = mm2pt(printSetting.stub_date_y) + offset_y
      const stub_payee_x = mm2pt(printSetting.stub_payee_x) + offset_x
      const stub_payee_y = mm2pt(printSetting.stub_payee_y) + offset_y
      const stub_amount_x = mm2pt(printSetting.stub_amount_x) + offset_x
      const stub_amount_y = mm2pt(printSetting.stub_amount_y) + offset_y
      const stub_desc_x = mm2pt(printSetting.stub_desc_x) + offset_x
      const stub_desc_y = mm2pt(printSetting.stub_desc_y) + offset_y
      const stub_payee_width = mm2pt(printSetting.stub_payee_width)
      const stub_font_size = printSetting.stub_font_size

      const font_size = printSetting.font_size
      const font_weight = printSetting.font_weight === 1
      const payee_width = mm2pt(printSetting.payee_width)
      const text_width = mm2pt(printSetting.text_width)
      const text_indent = printSetting.text_indent
      const line_height = printSetting.line_height
      const crossed_width = mm2pt(printSetting.crossed_width)
      const crossed_space = mm2pt(printSetting.crossed_space)

      const amount_n = `**${amountFormat(amount)}**`
      let text_amount = amount_n
      switch (language) {
        case 'en':
          text_amount = chequeConvertEnglish(amount)
          break
        case 'cn':
          text_amount = chequeConvertCN(amount)
          break
        case 'numeric':
          text_amount = amount_n
          break
        default:
          text_amount = chequeConvertEnglish(amount)
          break
      }

      const line_width = 0.3
      const line_space = 1

      const rsi = this.remoteServerInfo
      const background_img_url = isPreview
        ? `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/cheque/cheque_${chq_template_code}.gif`
        : ''
      const background_img = isPreview
        ? await getUrlBase64(background_img_url)
        : getEmptyImageBase64()
      const ac_payee_only_img_url = require('@/assets/cheque/ac-payee-only-200-3.png')
      const ac_payee_only_img = await getUrlBase64(ac_payee_only_img_url)

      const dataStr = dateUtil.format(new Date(date), 'dd/MM/yyyy')

      const arr = [
        {
          // 背景圖
          image: background_img,
          // text: '背景圖',
          width: cheque_width,
          // height: cheque_height,
          absolutePosition: { x: offset_x, y: offset_y },
        },
        {
          // 付款人
          absolutePosition: { x: payee_x, y: payee_y },
          width: payee_width,
          text: payee,
          fontSize: font_size,
          bold: font_weight,
        },
        {
          // 日期
          absolutePosition: { x: date_x, y: date_y },
          text: dataStr,
          fontSize: font_size,
          bold: font_weight,
        },
        {
          // 大寫金額
          absolutePosition: { x: text_amount_x, y: text_amount_y },
          fontSize: font_size,
          bold: font_weight,
          columns: [
            {
              // background: 'red',
              width: text_width,
              text: text_amount,
              lineHeight: line_height,
              leadingIndent: text_indent,
            },
          ],
        },
        {
          // 小寫金額
          absolutePosition: { x: amount_x, y: amount_y },
          text: amount_n,
          fontSize: font_size,
          bold: font_weight,
        },
      ]
      if (showUnderline && !isHideAC) {
        arr.push(
          {
            // A/C Payee Only
            image: ac_payee_only_img,
            // text: 'A/C Payee Only',
            width: cheque_height / 3,
            height: cheque_height / 3,
            absolutePosition: { x: ac_payee_only_x, y: ac_payee_only_y },
          },
          {
            // 刪或持票人
            absolutePosition: { x: crossed_bearer_x, y: crossed_bearer_y },
            canvas: [
              {
                // 'lineColor': 'gray',
                type: 'line',
                x1: 0,
                y1: 0,
                x2: crossed_width,
                y2: 0,
                lineWidth: line_width,
              },
              {
                type: 'line',
                x1: 0,
                y1: line_space,
                x2: crossed_width,
                y2: line_space,
                lineWidth: line_width,
              },
              {
                type: 'line',
                x1: 0,
                y1: crossed_space,
                x2: crossed_width,
                y2: crossed_space,
                lineWidth: line_width,
              },
              {
                type: 'line',
                x1: 0,
                y1: crossed_space + line_space,
                x2: crossed_width,
                y2: crossed_space + line_space,
                lineWidth: line_width,
              },
            ],
          },
        )
      }
      if (showChequeStub) {
        arr.push(
          {
            // 票根受款人
            absolutePosition: { x: stub_payee_x, y: stub_payee_y },
            fontSize: stub_font_size,
            bold: font_weight,
            columns: [
              {
                // background: 'red',
                width: stub_payee_width,
                text: payee,
                // lineHeight: line_height,
                // leadingIndent: text_indent
              },
            ],
          },
          {
            // 小寫金額
            absolutePosition: { x: stub_amount_x, y: stub_amount_y },
            text: amount_n,
            fontSize: stub_font_size,
            bold: font_weight,
          },
          {
            // 日期
            absolutePosition: { x: stub_date_x, y: stub_date_y },
            text: dataStr,
            fontSize: stub_font_size,
            bold: font_weight,
          },
        )
      }
      return arr
    },
    generatePDFCheque({ title, data, printSetting }) {
      return new Promise((resolve, reject) => {
        const page_width = mm2pt(printSetting.page_width)
        const page_height = mm2pt(printSetting.page_height)

        const docDefinition = {
          info: {
            title: title,
            author: 'Norray',
            subject: title,
          },
          content: data,
          styles: {},
          pageSize: {
            width: page_width,
            height: page_height,
          },
          defaultStyle: {
            font: 'cn',
            fontSize: 10,
          },
          pageMargins: [0, 0, 0, 0],
          pageBreakBefore: insertData(
            `function(currentNode, followingNodesOnPage, nodesOnNextPage, previousNodesOnPage) {
            return currentNode.headlineLevel === 1
          }`,
            {},
          ),
          // pageBreakBefore: function(currentNode, followingNodesOnPage, nodesOnNextPage, previousNodesOnPage) {
          //   return currentNode.headlineLevel === 1
          // }
        }
        // console.log(JSON.stringify(docDefinition))
        // openPdf(docDefinition)
        resolve(docDefinition)
      })
    },
    uniqueArr(array) {
      var r = []
      for (var i = 0, l = array.length; i < l; i++) {
        for (var j = i + 1; j < l; j++) {
          if (array[i] === array[j]) j === ++i
        }
        r.push(array[i])
      }
      return r
    },
  },
}
</script>
