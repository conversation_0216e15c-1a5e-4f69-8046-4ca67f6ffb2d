<script>
import { mapGetters } from 'vuex'
import { amountFormat } from '@/utils'
// import dateUtil from '@/utils/date'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import dateUtil from '@/utils/date'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import dayjs from 'dayjs'

export default {
  name: 'HandlePDF',
  mixins: [loadPrintoutSetting],
  data() {
    return {
      ps_code: 'pdftrialbalance',
      pdfKey: 'report.trialBalance.pdf.',
    }
  },
  computed: {
    ...mapGetters(['remoteServerInfo', 'school', 'currentDate']),
  },
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      return new Promise((resolve, reject) => {
        this.loadPrintoutSetting()
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(() => {
            if (this.currentFilter.mode === 1) {
              return this.formatPrintDataByCode(printSetting)
            } else {
              return this.formatPrintDataByTree(printSetting)
            }
          })
          .then(({ schoolInfo, pageInfo, columns, tableData }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              columns,
              tableData,
              printSetting,
            }).then(() => {
              resolve()
            })
          })
          .catch(err => {
            reject(err)
            console.error('onPrint', err)
          })
      })
    },
    formatPrintDataByCode(printSetting) {
      return new Promise(async(resolve, reject) => {
        const filters = this.currentFilter
        const language = printSetting.language
        const summary = this.summary
        const langKey = this.pdfKey
        const columnData = printSetting.columnData.sort((a, b) => a.position - b.position)

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        const title = this.$t('router.reportTrialBalance', language)
        const fund = this.funds.find(i => i.fund_id === filters.selectedFundId)
        const fundName = fund
          ? language === 'en'
            ? fund.fund_name_en
            : fund.fund_name_cn
          : this.$t('report.trialBalance.content.all', language)
        // if (date && date.length === 2) {
        //   dateStr = dateUtil.format(new Date(date[0]), 'dd/MM/yyyy') + ' - ' + dateUtil.format(new Date(date[1]), 'dd/MM/yyyy')
        // }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title} - ${fundName}`,
          data: [
            {
              label: this.$t('report.trialBalance.label.fund', language) + ':',
              value: fundName,
            },
          ],
        }

        // 表頭
        const columns = [[]]
        const secondRow = []
        let hasSecond = false
        const drGroup = {
          bf: 'bf_dr',
          mid: 'dr',
          end: 'end_dr',
        }
        const crGroup = {
          bf: 'bf_cr',
          mid: 'cr',
          end: 'end_cr',
        }
        columnData.forEach(item => {
          if (item.position === 0) {
            return
          }
          switch (item.name) {
            case 'bf':
            case 'mid':
            case 'end':
              columns[0].push({
                text:
                  this.$t(langKey + item.name, language) +
                  '\n' +
                  this._dataTypes(language)[item.name],
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 2,
                alignment: 'center',
                width: item.width,
              })
              columns[0].push({
                text: '',
                width: item.width,
              })
              secondRow.push({
                text: this.$t(langKey + drGroup[item.name], language),
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 1,
                alignment: item.alignment,
                width: item.width,
              })
              secondRow.push({
                text: this.$t(langKey + crGroup[item.name], language),
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 1,
                alignment: item.alignment,
                width: item.width,
              })
              hasSecond = true
              break
            default:
              {
                const key = item.name
                columns[0].push({
                  text: this.$t(langKey + key, language),
                  style: 'tableHeader',
                  rowSpan: 2,
                  alignment: item.alignment,
                  width: item.width,
                })

                secondRow.push({
                  text: '',
                })
              }
              break
          }
        })
        if (hasSecond) {
          columns.push(secondRow)
        }

        // 主數據
        //

        // tableData
        const tableData = []
        const sumRow = []
        let sumIndex = -1

        // 初始化統計行
        columnData.forEach((col, colIndex) => {
          if (col.position === 0) {
            // 即不顯示列
            return
          }
          switch (col.name) {
            case 'bf':
            case 'mid':
            case 'end': {
              sumRow.push({
                name: drGroup[col.name],
                text: amountFormat(summary[drGroup[col.name]]),
                style: 'tableFooter',
              })
              sumRow.push({
                name: drGroup[col.name],
                text: amountFormat(summary[crGroup[col.name]]),
                style: 'tableFooter',
              })
              if (sumIndex === -1) {
                sumIndex = colIndex
              }
              break
            }
            default: {
              sumRow.push({ name: col.name, text: '', style: 'tableFooter' })
            }
          }
        })
        this.tableData.forEach((item, rowIndex) => {
          const row = []
          columnData.forEach((col, colIndex) => {
            if (col.position === 0) {
              // 即不顯示列
              return
            }

            switch (col.name) {
              case 'bf':
              case 'mid':
              case 'end': {
                let dr = Number(item[drGroup[col.name]])
                isNaN(dr) && (dr = 0)
                row.push({
                  text: amountFormat(dr),
                  alignment: col.alignment,
                  style: 'tableContent',
                })
                let cr = Number(item[crGroup[col.name]])
                isNaN(cr) && (cr = 0)
                row.push({
                  text: amountFormat(cr),
                  alignment: col.alignment,
                  style: 'tableContent',
                })
                break
              }
              case 'name':
                row.push({
                  text: item[language === 'en' ? 'name_en' : 'name_cn'],
                  alignment: col.alignment,
                  style: 'tableContent',
                })
                break
              case 'index':
                row.push({
                  text: rowIndex + 1,
                  alignment: col.alignment,
                  style: 'tableContent',
                })
                break
              default: {
                row.push({
                  name: col.name,
                  text: item[col.name],
                  alignment: col.alignment,
                  style: 'tableContent',
                })
              }
            }
          })
          tableData.push(row)
        })

        if (sumIndex > 0) {
          sumRow[0].text = this.$t('print.total', language)
          sumRow[0].colSpan = sumIndex
          sumRow[0].alignment = 'right'
          sumRow[0].bold = true
          sumRow[0].style = 'tableFooter'
          tableData.push(sumRow)
        }

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    treeDataToArray(tree, level) {
      // const tree = this.tableData
      const filters = this.preferences.filters
      const currentLevel = filters.currentLevel
      const expanded_list = filters.expanded_list.split(',')
      const toArray = function(tree) {
        const array = []
        tree.forEach((item, index) => {
          let expanded = false
          const newItem = Object.assign(
            {
              code: '',
              name_cn: '',
              name_en: '',
              level: 1,
              bf_dr: 0,
              bf_cr: 0,
              dr: 0,
              cr: 0,
              end_dr: 0,
              end_cr: 0,
            },
            item,
          )
          array.push(newItem)

          if (currentLevel === 0) {
            if (!expanded_list.includes(item.fund_id + '')) {
              return
            }
            expanded = true
          } else {
            if (item.level > currentLevel) {
              if (item.level !== currentLevel) {
                expanded = true
              }
              return
            }
          }

          if (item.children && item.children.length) {
            newItem.expanded = expanded
            array.push(...toArray(item.children))
          }
        })
        return array
      }

      const data = toArray(tree)
      return data
    },
    _dateStart(language) {
      if (!this.currentFilter.begin_date) {
        return ''
      }
      const date = dateUtil.format(new Date(this.currentFilter.begin_date), 'dd/MM/yyyy')
      return this.$t('report.trialBalance.label.dateTo', language, { date })
    },
    _dateEnd(language) {
      if (!this.currentFilter.end_date) {
        return ''
      }
      const date = dateUtil.format(new Date(this.currentFilter.end_date), 'dd/MM/yyyy')
      return this.$t('report.trialBalance.label.dateTo', language, { date })
    },
    _dateRangeStr(language) {
      if (!this.currentFilter.begin_date || !this.currentFilter.end_date) {
        return ''
      }
      const date1 = dateUtil.format(new Date(this.currentFilter.begin_date), 'dd/MM/yyyy')
      const date2 = dateUtil.format(new Date(this.currentFilter.end_date), 'dd/MM/yyyy')
      return date1 + ' - ' + date2
    },
    _dataTypes(language) {
      return {
        bf: this._dateStart(language),
        mid: this._dateRangeStr(language),
        end: this._dateEnd(language),
      }
    },
    formatPrintDataByTree(printSetting) {
      return new Promise(async(resolve, reject) => {
        const filters = this.currentFilter
        const language = printSetting.language
        const summary = this.summary
        const langKey = this.pdfKey
        const data = this.treeDataToArray(this.tableData)

        const columnData = printSetting.columnData.sort((a, b) => a.position - b.position)

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        const title = this.$t('router.reportTrialBalance', language)
        const fund = this.funds.find(i => i.fund_id === filters.selectedFundId)
        const fundName = fund
          ? language === 'en'
            ? fund.fund_name_en
            : fund.fund_name_cn
          : this.$t('report.trialBalance.content.all', language)
        // if (date && date.length === 2) {
        //   dateStr = dateUtil.format(new Date(date[0]), 'dd/MM/yyyy') + ' - ' + dateUtil.format(new Date(date[1]), 'dd/MM/yyyy')
        // }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title} - ${fundName}`,
          data: [
            {
              label: this.$t('report.trialBalance.label.fund', language) + ':',
              value: fundName,
            },
          ],
        }

        // 表頭
        const columns = [[]]
        const secondRow = []
        let hasSecond = false
        const drGroup = {
          bf: 'bf_dr',
          mid: 'dr',
          end: 'end_dr',
        }
        const crGroup = {
          bf: 'bf_cr',
          mid: 'cr',
          end: 'end_cr',
        }
        columnData.forEach(item => {
          if (item.position === 0) {
            return
          }
          switch (item.name) {
            case 'bf':
            case 'mid':
            case 'end':
              columns[0].push({
                text:
                  this.$t(langKey + item.name, language) +
                  '\n' +
                  this._dataTypes(language)[item.name],
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 2,
                alignment: 'center',
                width: item.width,
              })
              columns[0].push({
                text: '',
                width: item.width,
              })
              secondRow.push({
                text: this.$t(langKey + drGroup[item.name], language),
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 1,
                alignment: 'center',
                width: item.width,
              })
              secondRow.push({
                text: this.$t(langKey + crGroup[item.name], language),
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 1,
                alignment: 'center',
                width: item.width,
              })
              hasSecond = true
              break
            default:
              {
                const key = item.name
                columns[0].push({
                  text: this.$t(langKey + key, language, language),
                  style: 'tableHeader',
                  rowSpan: 2,
                  alignment: 'center',
                  width: item.width,
                })

                secondRow.push({
                  text: '',
                })
              }
              break
          }
        })
        if (hasSecond) {
          columns.push(secondRow)
        }

        // 主數據
        //

        // tableData
        const tableData = []
        const sumRow = []
        let sumIndex = -1

        // 初始化統計行
        columnData.forEach((col, colIndex) => {
          if (col.position === 0) {
            // 即不顯示列
            return
          }
          switch (col.name) {
            case 'bf':
            case 'mid':
            case 'end': {
              sumRow.push({
                name: drGroup[col.name],
                text: amountFormat(summary[drGroup[col.name]]),
                style: 'tableFooter',
              })
              sumRow.push({
                name: drGroup[col.name],
                text: amountFormat(summary[crGroup[col.name]]),
                style: 'tableFooter',
              })
              if (sumIndex === -1) {
                sumIndex = colIndex
              }
              break
            }
            default: {
              sumRow.push({ name: col.name, text: '', style: 'tableFooter' })
            }
          }
        })
        data.forEach((item, rowIndex) => {
          const row = []
          columnData.forEach((col, colIndex) => {
            if (col.position === 0) {
              // 即不顯示列
              return
            }
            const cell = {
              text: '',
              alignment: col.alignment,
              style: 'tableContent',
            }

            switch (col.name) {
              case 'bf':
              case 'mid':
              case 'end': {
                let dr = Number(item[drGroup[col.name]])
                isNaN(dr) && (dr = 0)
                row.push({
                  text: amountFormat(dr),
                  alignment: col.alignment,
                  style: 'tableContent',
                })
                let cr = Number(item[crGroup[col.name]])
                isNaN(cr) && (cr = 0)
                // row.push({
                //   text: amountFormat(cr),
                //   alignment: col.alignment,
                //   style: 'tableContent'
                // })

                cell.text = amountFormat(cr)
                break
              }
              case 'name':
                cell.text = language === 'en' ? item.name_en : item.name_cn
                // row.push({
                //   text: item[language === 'en' ? 'name_en' : 'name_cn'],
                //   alignment: col.alignment,
                //   style: 'tableContent'
                // })
                break
              case 'index':
                cell.text = Array(item.level + 1).join('*')
                // row.push({
                //   text: Array(item.level + 1).join('*'),
                //   alignment: col.alignment,
                //   style: 'tableContent'
                // })
                break
              default: {
                cell.text = item[col.name]
                // row.push({
                //   name: col.name,
                //   text: item[col.name],
                //   alignment: col.alignment,
                //   style: 'tableContent'
                // })
              }
            }
            row.push(cell)
          })

          if (!item.code) {
            row.forEach(i => {
              // i.fillColor = '#f3f3f3'
              i.style = 'tableContentGroup'
            })
          }
          tableData.push(row)
        })

        if (sumIndex > 0) {
          sumRow[0].text = this.$t('print.total', language)
          sumRow[0].colSpan = sumIndex
          sumRow[0].alignment = 'right'
          sumRow[0].bold = true
          sumRow[0].style = 'tableFooter'
          tableData.push(sumRow)
        }

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    async showPDF({ schoolInfo, pageInfo, columns, tableData, printSetting }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      const title_width = mm2pt(printSetting.title_width)

      const bottom_sign = printSetting.sign_style.toString() === '2'
      // 表格寬度
      const widths = generateWidths(columns[0])

      // 學校信息
      const schoolTable = generateSchoolInfo(
        schoolInfo.name_cn,
        schoolInfo.name_en,
        schoolInfo.logo,
      )
      // 頁面信息
      const pageTable = generatePageInfo(
        pageInfo.title,
        pageInfo.filename,
        pageInfo.data,
        title_width,
        margin_right,
      )
      // 頁頭，包含LOGO，頁面信息
      const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns[0].length)

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height // + 20// +50
      }

      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: [
          {
            // Content
            width: '100%',
            style: 'tableExample',
            table: {
              dontBreakRows: true,
              keepWithHeaderRows: 1,
              // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
              widths: widths,
              heights: tableData.map((e, i) =>
                i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto',
              ),
              headerRows: columns.length + 1,
              body: [
                pageHeader, // 頁頭
                ...columns, // 數據表頭
                ...tableData, // 數據
              ],
            },
            layout: {
              vLineWidth: lineWidth,
              hLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
            },
          },
        ],
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          tableContentGroup: {
            fillColor: '#dedede',
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      if (printSetting.sign_style.toString() === '1') {
        // 浮動
        docDefinition.content.push(signTable)
      }
      console.log(JSON.stringify(docDefinition))
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1

      await openPdf(docDefinition)
    },
  },
}
</script>
