<template>
  <div id="customStyleDialog" class="styleDialog">
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :show-close="false"
      :close-on-click-modal="true"
      :title="title"
      width="60%"
      @open="openDialog"
    >
      <div style="min-width: 640px">
        <el-form v-loading="loading" label-width="120px" label-position="right">
          <!-- Table 位置 -->
          <el-form-item :label="$t('style.position')">
            <!--            <el-row :gutter="20">-->
            <!--              <el-col-->
            <!--                v-for="(column, index) in positionOptions"-->
            <!--                v-if="column.ss_id"-->
            <!--                :key="column.ss_id"-->
            <!--                :xs="24"-->
            <!--                :sm="12"-->
            <!--                :md="8"-->
            <!--                :lg="6"-->
            <!--                :xl="4"-->
            <!--              >-->
            <!--                <div class="flex" >-->
            <!--                  <span class="num">#{{ index }}：</span>-->
            <!--                  <el-select v-model="positions[index]">-->
            <!--                    <el-option-->
            <!--                      v-for="item in positionOptions"-->
            <!--                      :key="item.ss_id"-->
            <!--                      :value="item.ss_id"-->
            <!--                      :label="$t(handleLabel(item))"-->
            <!--                    >-->
            <!--                      {{ $t(handleLabel(item)) }}-->
            <!--                    </el-option>-->
            <!--                  </el-select>-->
            <!--                </div>-->
            <!--              </el-col>-->
            <!--            </el-row>-->

            <el-drag-select
              v-model="positions"
              :placeholder="$t('placeholder.selectDragSort')"
              :filter-text="['#']"
              style="width: 500px"
              multiple
              @change="onChangePositions"
            >
              <!-- <el-option
                v-if="tableType === 'single'"
                label="#"
                value="_index" /> -->
              <el-option
                v-for="(item, index) in options"
                :key="index"
                :label="$t(langKey + item)"
                :value="item"
              />
            </el-drag-select>
          </el-form-item>
          <div class="mini-form">
            <!-- 左欄寬度 -->
            <el-form-item
              v-if="tableType !== 'full-screen-without-first-field'"
              :label="$t('style.leftWidth')"
            >
              <div class="dialog_item">
                <el-input-number
                  v-model="leftViewWidth"
                  :controls="false"
                  :min="30"
                  :max="70"
                  class="widthInput"
                />
              </div>
            </el-form-item>
            <!-- 主題色 -->
            <el-form-item v-if="hasTheme" :label="$t('style.theme')">
              <div class="dialog_item">
                <el-color-picker v-model="theme" size="mini" />
                <!--<el-input-number :controls="false" v-model="lineHeight" :min="5" :max="70" class="widthInput"/>-->
              </div>
            </el-form-item>
            <!-- tree寬度 -->
            <el-form-item v-if="tableType === 'tree'" :label="$t(langKey + 'first_field')">
              <div class="dialog_item">
                <el-input-number
                  v-model="firstFieldWidth"
                  :controls="false"
                  :min="30"
                  class="widthInput"
                />
              </div>
            </el-form-item>
            <el-form-item
              v-for="column in filteredEditData"
              :key="column.ss_id + column.ss_key"
              :label="column.ss_key === '_index' ? '#' : $t(langKey + column.ss_key)"
            >
              <div class="dialog_item">
                <el-input-number
                  v-model="column.width"
                  :controls="false"
                  :min="30"
                  class="widthInput"
                />
                <el-select v-model="column.alignment" class="select-align">
                  <el-option
                    v-for="item in alignmentOptions"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="onCancel">{{ $t('button.cancel') }}</el-button>
        <el-button size="mini" type="info" @click="onLoadDefault">{{
          $t('button.loadDefault')
        }}</el-button>
        <el-button size="mini" type="info" @click="onSaveDefault">{{
          $t('button.saveDefault')
        }}</el-button>
        <el-button size="mini" type="danger" @click="initData">{{ $t('button.reset') }}</el-button>
        <el-button size="mini" type="primary" @click="onSave">{{ $t('button.update') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import {
  getUserStyleSheets,
  editUserStyleSheets,
  getDefaultStyleSheets,
  editDefaultStyleSheets,
  editStaffStyleSheets,
  getStaffStyleSheets,
} from '@/api/settings/user/style'

import ElDragSelect from '@/components/DragSelect' // base on element-ui

export default {
  components: { ElDragSelect },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: 'Setting',
    },
    langKey: {
      type: String,
      default: 'master.user.label.',
    },
    tableType: {
      type: String,
      default: 'single',
    },
    ss_code: {
      type: String,
      default: null,
    },
    hasTheme: {
      type: Boolean,
      default: false,
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
  },
  // props: ['dialogVisible'],
  data() {
    return {
      form: {},
      loading: true,
      editData: [],
      editDataTemp: [],
      positionOptions: [],
      positions: [],
      leftViewWidth: 50,
      firstFieldWidth: 150,
      theme: '#3e97dd',
      // permission_code: null
    }
  },
  computed: {
    ...mapGetters(['system', 'user_id']),
    alignmentOptions() {
      return [
        {
          label: this.$t('style.left'),
          value: 'left',
        },
        {
          label: this.$t('style.center'),
          value: 'center',
        },
        {
          label: this.$t('style.right'),
          value: 'right',
        },
      ]
    },
    options() {
      return this.columns.map(i => i)
    },
    permission_code() {
      return this.ss_code ? this.ss_code : this.$route.meta.p_code
    },
    filteredEditData() {
      return this.editData.filter(column => column.ss_type === 'column')
    },
  },
  created() {
    // this.permission_code = this.ss_code ? this.ss_code : this.$route.meta.p_code
  },
  mounted() {},
  activated() {},
  methods: {
    openDialog() {
      this.initData()
    },
    initData() {
      this.loading = true
      this.generateDefaultData()
        .then(this.fetchData)
        .then(this.loadData)
        .catch(() => {})
        .finally(() => {
          this.$nextTick(() => {
            this.loading = false
          })
        })
    },
    fetchData() {
      const system = this.system
      const ss_code = this.ss_code ? this.ss_code : this.$route.meta.p_code
      let user_id = this.user_id
      let staff_id = this.user_id

      let api = getStaffStyleSheets
      if (system === 'AC') {
        api = getUserStyleSheets
        staff_id = undefined
      } else {
        user_id = undefined
      }
      return new Promise((resolve, reject) => {
        api({ user_id, staff_id, system, ss_code })
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    generateDefaultData() {
      return new Promise((resolve, reject) => {
        let index = 1
        const editData = [
          {
            ss_id: '',
            user_id: null,
            staff_id: null,
            system: this.system,
            ss_code: this.permission_code,
            ss_type: 'screen',
            seq: index++,
            ss_key: 'scale',
            value: '50',
            alignment: null,
            width: null,
            x: null,
            y: null,
          },
        ]
        if (this.hasTheme) {
          editData.push({
            ss_id: '',
            user_id: null,
            staff_id: null,
            system: this.system,
            ss_code: this.permission_code,
            ss_type: 'screen',
            seq: index++,
            ss_key: 'theme',
            value: '#3e97dd',
            alignment: null,
            width: null,
            x: null,
            y: null,
          })
        }
        const defaultItem = {
          ss_id: '',
          user_id: null,
          staff_id: null,
          system: this.system,
          ss_code: this.permission_code,
          ss_type: 'column',
          seq: 4,
          ss_key: '',
          value: null,
          alignment: 'left',
          width: 100,
          x: null,
          y: null,
        }
        switch (this.tableType) {
          case 'tree':
            editData.push({
              ss_id: '',
              user_id: null,
              staff_id: null,
              system: this.system,
              ss_code: this.permission_code,
              ss_type: 'table',
              seq: index++,
              ss_key: 'first_field',
              value: null,
              alignment: null,
              width: '150',
              x: null,
              y: null,
            })
            break
          case 'full-screen-without-first-field':
            break
          case 'full-screen-with-first-field':
          default:
            editData.push({
              ss_id: '',
              user_id: null,
              staff_id: null,
              system: this.system,
              ss_code: this.permission_code,
              ss_type: 'column',
              seq: index++,
              ss_key: '_index',
              value: null,
              alignment: 'left',
              width: '50',
              x: null,
              y: null,
            })

            break
        }

        this.columns.forEach((column, i) => {
          const newColumn = Object.assign({}, defaultItem)
          newColumn.ss_type = 'column'
          newColumn.ss_id = ''
          newColumn.seq = index + i + 1
          // newColumn.x = index + 4
          newColumn.ss_key = column
          editData.push(newColumn)
        })
        this.editDataTemp = editData
        resolve(editData)
      })
    },
    loadData(styleData) {
      const positionOptions = [
        {
          ss_type: 'column',
          seq: 0,
          x: 0,
          y: 0,
          ss_key: '---',
          ss_id: '',
          value: '',
        },
      ]
      const editData = this.editDataTemp
      // 獲取已保存的設置
      styleData
        .filter(item => item.ss_type === 'column')
        .forEach(item => {
          const defaultItem = editData.find(defaultItem => defaultItem.ss_key === item.ss_key)

          if (defaultItem) {
            defaultItem.ss_id = item.ss_id
            defaultItem.user_id = item.user_id
            defaultItem.staff_id = item.staff_id
            defaultItem.system = item.system
            defaultItem.ss_code = item.ss_code
            defaultItem.ss_type = item.ss_type
            defaultItem.seq = item.seq
            defaultItem.ss_key = item.ss_key
            defaultItem.value = item.value
            defaultItem.alignment = item.alignment
            defaultItem.width = item.width
            defaultItem.x = item.x
            defaultItem.y = item.y
          }
        })
      // 生成選擇項
      editData
        .filter(item => item.ss_type === 'column')
        .forEach(item => {
          // 選擇項
          positionOptions.push({
            ss_type: item.ss_type,
            seq: item.seq,
            x: item.x,
            y: item.y,
            ss_key: item.ss_key,
            ss_id: item.ss_id,
            value: item.value,
          })
        })
      // 左欄寬度設置
      const scale = styleData.find(item => {
        return item.ss_type === 'screen' && item.ss_key === 'scale'
      })
      if (scale) {
        this.leftViewWidth = scale.value
      }
      if (this.hasTheme) {
        // 主題色設置
        const theme = styleData.find(item => {
          return item.ss_type === 'screen' && item.ss_key === 'theme'
        })
        if (theme) {
          this.theme = theme.value
        }
      }
      /* firstField寬度 */
      const firstField = styleData.find(item => {
        return item.ss_type === 'table' && item.ss_key === 'first_field'
      })
      if (firstField) {
        this.firstFieldWidth = firstField.width
      }
      // 排序
      // positionOptions.sort((a, b) => {
      //   console.log('sort', a.x, b.x)
      //   return a.x - b.x
      // })
      // positionOptions.forEach(item => {
      //   positions.push(item.ss_id)
      // })
      // 下拉選項
      const positions = [] // this.columns.map(i => i)
      // 排序

      editData
        .filter(item => item.ss_type === 'column' && item.x)
        .sort((a, b) => {
          return a.x - b.x
        })
        .map(i => i)
        .forEach((item, i) => {
          if (item.x > 0) {
            positions.push(item.ss_key)
          }
        })
      // for (let i = 1, num = 1; i < positionOptions.length; i++) {
      //   if (positionOptions[i].x) {
      //     positions[num++] = positionOptions[i].ss_id
      //   }
      // }

      this.positionOptions = positionOptions
      this.positions = positions.sort(this.positionsSort).filter(i => i !== '_index')
      console.log(this.positions, 'positions')
      this.editData = editData
    },
    positionsSort(a, b) {
      if (a === '_index') {
        return -1
      } else {
        return 0
      }
    },
    onCancel(done) {
      this.$emit('update:dialogVisible', false)
    },
    getSettingJSON() {
      const list = this.positions.sort(this.positionsSort)
      const data = this.editData
      /* 處理排序 */
      data.forEach(item => {
        const index = list.findIndex(i => i === item.ss_key)
        if (index !== -1) {
          item.x = index + 1
          // item.seq = index + 1
        } else {
          item.x = 0
          // item.seq = index
        }
      })
      if (
        data.filter(i => i.ss_type === 'column').length > 0 &&
        data.filter(i => i.x).length === 0
      ) {
        this.$message.error(this.$t('message.showColumn'))
        return
      }

      /* 左欄寬度 */
      const scale = data.find(item => {
        return item.ss_type === 'screen' && item.ss_key === 'scale'
      })
      if (scale) {
        scale.value = this.leftViewWidth
      } else {
        throw new Error(this.$t('message.configurationError'))
      }
      if (this.hasTheme) {
        /* 主題 */
        const theme = data.find(item => {
          return item.ss_type === 'screen' && item.ss_key === 'theme'
        })
        if (theme) {
          theme.value = this.theme
        } else {
          throw new Error(this.$t('message.configurationError'))
        }
      }
      /* firstField寬度 */
      const firstField = data.find(item => {
        return item.ss_type === 'table' && item.ss_key === 'first_field'
      })
      if (firstField) {
        firstField.width = this.firstFieldWidth
      }

      return this.editData
    },
    onSave() {
      let user_id = this.user_id
      let staff_id = this.user_id
      const system = this.system
      const ss_code = this.permission_code
      const ss_setting_json = this.getSettingJSON()
      if (!ss_setting_json) {
        return
      }
      let api = editStaffStyleSheets
      if (system === 'AC') {
        api = editUserStyleSheets
        staff_id = undefined
      } else {
        user_id = undefined
      }
      api({ user_id, staff_id, system, ss_code, ss_setting_json })
        .then(res => {
          this.$message.success(this.$t('message.success'))
          this.$emit('update:dialogVisible', false)
          this.$emit('reloadStyleSheets')
          this.$emit('close')
        })
        .catch(() => {})
    },
    onLoadDefault() {
      this.loading = true
      const system = this.system
      const ss_code = this.ss_code ? this.ss_code : this.$route.meta.p_code
      this.generateDefaultData()
        .then(() => {
          return getDefaultStyleSheets({ system, ss_code })
        })
        .then(this.loadData)
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    onSaveDefault() {
      const system = this.system
      const ss_code = this.permission_code
      const ss_setting_json = this.getSettingJSON()
      editDefaultStyleSheets({ system, ss_code, ss_setting_json })
        .then(res => {
          this.$message.success(this.$t('message.success'))
        })
        .catch(() => {})
    },
    handleLabel(item) {
      if (item.ss_id) {
        if (item.ss_key === '_index') {
          return '#'
        } else {
          return this.langKey + item.ss_key
        }
      } else {
        return '---'
      }
    },
    onChangePositions(val) {
      this.positions = this.positions.sort(this.positionsSort)
    },
  },
}
</script>
<style scoped>
.dialog_item {
  display: flex;
}
</style>
<style lang="scss">
.flex {
  display: flex;
  margin-right: 10px;
  width: 150px;
  .num {
    width: 50px;
  }
}
.dialog_item .el-input {
  width: 120px;
  margin-right: 20px;
}
.styleDialog .el-dialog__header {
  padding: 20px 20px 0px;
  /* color: #2e9eff */
}
.styleDialog .el-dialog__header .el-dialog__title {
  color: #2e9eff;
  font-weight: bold;
}
.styleDialog .el-dialog__body {
  padding: 20px 20px;
}
.styleDialog .el-form-item {
  margin-bottom: 10px;
}
.el-dialog__body {
  max-height: 561px;
  overflow: auto;
}

#customStyleDialog.styleDialog {
  .select-align {
    .el-input {
      width: 100px;
    }
  }
  .el-form {
    .el-form-item {
      .el-form-item__label {
        width: 140px !important;
      }
      .el-form-item__content {
        margin-left: 100px;
      }
    }
    .widthInput {
      width: 50px;
      margin-right: 10px;
      .el-input {
        line-height: 30px;
      }
      input {
        width: 50px;
        padding: 0 5px;
        text-align: center;
      }
    }
  }
}
</style>
