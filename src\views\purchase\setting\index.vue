<template>
  <div class="purchase-setting">
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item>
        {{ $t('router.master') }}
      </el-breadcrumb-item>
      <el-breadcrumb-item>
        {{ $t($route.meta.title) }}
      </el-breadcrumb-item>
    </el-breadcrumb>
    <el-form ref="form" v-loading="loading" :model="form" :inline="true" label-width="100px">
      <el-form-item :label="$t(langKey + 'principal_cn')" prop="principal_cn.value">
        <el-input v-model="form.principal_cn.value" class="input" />
      </el-form-item>
      <el-form-item :label="$t(langKey + 'principal_en')" prop="principal_en.value">
        <el-input v-model="form.principal_en.value" class="input" />
      </el-form-item>
      <br>
      <el-form-item :label="$t(lang<PERSON><PERSON> + 'sch_tel')" prop="sch_tel.value">
        <el-input v-model="form.sch_tel.value" class="input" />
      </el-form-item>
      <el-form-item :label="$t(langKey + 'sch_fax')" prop="sch_fax.value">
        <el-input v-model="form.sch_fax.value" class="input" />
      </el-form-item>
      <br>
      <el-form-item :label="$t(langKey + 'sch_addr_cn')" prop="sch_addr_cn.value">
        <el-input
          v-model="form.sch_addr_cn.value"
          :autosize="textAreaSize"
          type="textarea"
          class="input-long"
        />
      </el-form-item>
      <br>
      <el-form-item :label="$t(langKey + 'sch_addr_en')" prop="sch_addr_en.value">
        <el-input
          v-model="form.sch_addr_en.value"
          :autosize="textAreaSize"
          type="textarea"
          class="input-long"
        />
      </el-form-item>
      <br>
      <el-form-item label=" " prop="no_month_skip.value">
        <el-checkbox v-model="form.no_month_skip.value" true-label="1" false-label="0">
          <span> {{ $t(langKey + 'no_month_skip') }}</span>
        </el-checkbox>
      </el-form-item>
      <br>
      <el-form-item label=" " prop="no_budget_skip.value">
        <el-checkbox v-model="form.no_budget_skip.value" true-label="1" false-label="0">
          <span> {{ $t(langKey + 'no_budget_skip') }}</span>
        </el-checkbox>
      </el-form-item>
      <br>
      <el-form-item label-width="100px" class="actions" label=" ">
        <el-button size="mini" type="primary" @click="onSave">
          {{ $t('button.edit') }}
        </el-button>
        <el-button size="mini" @click="onReset">
          {{ $t('button.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { editSystemSettings, fetchSystemSettings } from '@/api/settings/system'

export default {
  name: 'PurchaseSetting',
  data() {
    return {
      loading: false,
      langKey: 'purchase.master.setting.',
      form: {
        sch_tel: {
          system_setting_id: '',
          system: '',
          key: '',
          value: '',
          seq: '',
          display: '',
        },
        sch_fax: '',
        sch_addr_cn: '',
        sch_addr_en: '',
        principal_cn: '',
        principal_en: '',
        no_month_skip: '',
        no_budget_skip: '',
      },
      textAreaSize: {
        minRows: 3,
        maxRows: 5,
      },
    }
  },
  created() {
    this.onReset()
  },
  methods: {
    onSave() {
      this.$refs.form.validate((valid, a) => {
        if (!valid) return
        this.loading = true
        const settings = {}
        // this.form.hasOwnProperty(item) &&
        for (const key in this.form) {
          if (this.form.hasOwnProperty(key)) {
            const item = this.form[key]
            if (item.hasOwnProperty('system_setting_id')) {
              settings[item.system_setting_id] = item.value || ''
            }
          }
        }

        editSystemSettings({ settings })
          .then(() => {
            this.$message.success(this.$t('message.success'))
          })
          .catch(err => {
            console.log(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    onReset() {
      this.loading = true
      fetchSystemSettings('PC')
        .then(res => {
          this.form = res.reduce((obj, row) => {
            obj[row.key.replace(/^pc\./, '')] = {
              system_setting_id: row.system_setting_id,
              system: row.system,
              key: row.key,
              value: row.value,
              seq: row.seq,
              display: row.display,
            }
            return obj
          }, {})
        })
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.purchase-setting {
  padding: 20px;
  .breadcrumb {
    padding-bottom: 20px;
  }

  .input {
    width: 200px;
  }
  .input-long {
    width: 520px;
  }
}
</style>
