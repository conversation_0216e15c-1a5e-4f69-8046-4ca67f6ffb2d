<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :loading="loading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <!-- 賬目類別 -->
        <el-select v-model="preferences.filters.selectedFundType" class="year" @change="fetchTree">
          <el-option
            v-for="item in fundtypes"
            :key="item.value"
            :label="item.fund_name_cn"
            :value="item.fund_id"
          >
            <span v-if="item.label">{{ item.label }}</span>
          </el-option>
        </el-select>

        <!-- 會計週期 -->
        <el-select v-model="preferences.filters.year" class="year" @change="fetchTree">
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>
        <svg-icon v-if="hasPermission_Add" icon-class="new_folder" @click="onAddType" />
        <svg-icon v-if="hasPermission_Add" icon-class="new_file" @click="onAdd" />
        <!-- 層級數 -->
        <div v-for="i in accountLevel" :key="i" class="selectLevel" @click="expandLevel(i)">
          {{ i }}
        </div>
        <!-- 選擇層級 -->
        <div class="selectLevel" @click="enableYear">
          {{ preferences.filters.isEnableYear ? 'N' : 'Y' }}
        </div>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 導入 -->
          <div
            v-if="hasPermission_Input"
            :title="$t('btnTitle.importExcel')"
            class="icon import"
            @click="importDialog = true"
          >
            <svg-icon icon-class="import" class="action-icon" />
          </div>
          <!-- 導出 -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcel')"
            class="icon export"
            @click="onExport"
          >
            <svg-icon icon-class="export" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <tree-table
          ref="TreeTable"
          :data="accountTree"
          :eval-func="func"
          :eval-args="args"
          :expand-all="true"
          :first-field="language === 'en' ? 'name_en' : 'name_cn'"
          :first-field-align="firstFieldAlign"
          :first-field-width="firstFieldWidth"
          number-field="code"
          folder-field="fund_id"
          border
          @changeWidth="changeColumnWidth"
          @changeExpanded="onChangeExpanded"
        >
          <!--    編號姓名      -->
          <template v-if="scope && scope.row" slot="firstField" slot-scope="{ scope }">
            <i
              v-if="scope.row.fund_id"
              :class="
                'edac-icon edac-icon-folder' +
                  (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year)
                    ? ' activate'
                    : '')
              "
            />
            <i v-else class="edac-icon edac-icon-file" />
            <span
              v-if="!scope.row.fund_id"
              :class="
                'number-field' +
                  (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year)
                    ? ' selected'
                    : '')
              "
            >{{ scope.row.code }}</span>
            <span class="key">{{ scope.row[language === 'en' ? 'name_en' : 'name_cn'] }}</span>
          </template>

          <!-- <el-table-column label="BIE" width="50" min-width="50">
            <template v-if="scope && scope.row" slot-scope="scope">

              {{ scope.row.ac_B == "Y" ? 'B':'' }}
              {{ scope.row.ac_I == "Y" ? 'I':'' }}
              {{ scope.row.ac_E == "Y" ? 'E':'' }}

            </template>
          </el-table-column>
          <el-table-column :label="$t('master.account.label.ac_abbr_cn')" width="100">
            <template v-if="scope && scope.row" slot-scope="scope">
              <span v-if="scope.row.account_id">
                {{ scope.row.abbr_cn }}
              </span>
            </template>
          </el-table-column> -->
          <el-table-column
            v-for="item in filteredStyleColumns"
            :key="item.ss_id"
            :label="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :width="item.width"
            :property="item.ss_key"
            :column-key="item.ss_key"
            show-overflow-tooltip
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span v-if="item.ss_key === 'ac_bie'">
                {{ scope.row.ac_B == 'Y' ? 'B' : '' }}
                {{ scope.row.ac_I == 'Y' ? 'I' : '' }}
                {{ scope.row.ac_E == 'Y' ? 'E' : '' }}
              </span>
              <span v-else>
                {{ scope.row[item.ss_key] }}
              </span>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            :label="$t('table.action')"
            align="left"
            header-align="left"
            min-width="190"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <div v-if="scope.row.fund_id" class="operation_icon">
                <svg-icon
                  v-if="hasPermission_Add"
                  icon-class="new_folder"
                  @click="onAddType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <svg-icon v-if="hasPermission_Add" icon-class="new_file" @click="onAdd(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditType(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i
                  v-if="
                    hasPermission_Delete && (!scope.row.children || scope.row.children.length === 0)
                  "
                  class="el-icon-close"
                  @click="onDeleteType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
              </div>
              <div v-else class="operation_icon">
                <svg-icon class="no-cursor" icon-class="" />
                <svg-icon class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
                <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
              </div>
            </template>
          </el-table-column>
        </tree-table>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-object="editObject"
        :edit-parent="editParent"
        :default-parent="preferences.filters.selectedFundType"
        :fy-code="preferences.filters.year"
        :table-data="accountTree"
        @onCancel="onViewCancel"
      />
      <addTypePage
        v-else-if="leftView === 'addAccountType' || leftView === 'editAccountType'"
        :edit-object="editTypeObject"
        :edit-parent="editParent"
        :default-parent="preferences.filters.selectedFundType"
        :fy-code="preferences.filters.year"
        :table-data="accountTree"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      table-type="tree"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      class="upload-dialog"
      width="450px"
    >
      <UploadExcel :on-success="onImport" :on-template="onExport" />
    </el-dialog>
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import customStyle from '@/views/customStyle/index.vue'
import addPage from './add'
import addTypePage from './addType'
import { deleteAccount, getAccountTree, exportAccount, importAccount } from '@/api/master/account'
import { deleteFund, fetchFunds } from '@/api/master/funds'
import { mapGetters } from 'vuex'
import treeToArray from '@/components/TreeTable/eval.js'
import treeTable from '@/components/TreeTable'
import UploadExcel from '@/components/UploadExcel/index'
import { fetchYears } from '@/api/master/years'
import mixinPermission from '@/views/mixins/permission'
import { exportExcel, importExcel } from '@/utils/excel'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPreferences from '@/views/mixins/loadPreferences'
export default {
  name: 'MasterAccountIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    addTypePage,
    treeTable,
    UploadExcel,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      showDialog: false,
      leftView: '',
      funds: [],
      editObject: null,
      editTypeObject: null,
      func: treeToArray,
      expandAll: false,
      editParent: null,
      args: [null, null, 'timeLine'],
      accountTree: [],
      years: [],
      selectedYear: '',
      accountLevel: 0,
      currentLevel: 99,
      isEnableYear: false,
      importDialog: false,
      loading: false,
      fundtypes: [],
      exportFileName: 'accounts',
      langKey: 'master.account.label.',
      tableColumns: ['ac_bie', 'abbr_cn', 'abbr_en', 'nature_code', 'ac_bank', 'ac_group', 'code'],

      preferences: {
        filters: {
          year: '',
          currentLevel: 99,
          isEnableYear: false,
          expandedList: '',
          selectedFundType: '',
        },
      },
    }
  },
  computed: {
    ...mapGetters(['language']),
    filteredStyleColumns() {
      return this.styleColumns.filter(item => item.ss_key !== 'first_field')
    },
  },
  created() {
    this.fetchData()
  },
  methods: {
    expandLevel(level) {
      if (level) {
        this.$refs.TreeTable.showLevel(level)
        this.preferences.filters.currentLevel = level
        this.preferences.filters.expandedList = ''
      } else {
        this.$refs.TreeTable.setExpandItem(this.preferences.filters.expandedList)
      }
    },
    validateFilter() {
      if (!this.preferences.filters.selectedFundType) {
        this.$message.error(this.$t('message.pleaseSelectFund'))
        return false
      }
      return true
    },
    handleMaxLevel() {
      this.$nextTick(() => {
        this.accountLevel = this.$refs.TreeTable.getMaxLevel()
      })
    },
    enableYear() {
      this.preferences.filters.isEnableYear = !this.preferences.filters.isEnableYear

      if (!this.validateFilter()) {
        return
      }
      this.fetchTree()
    },
    onAdd(scope) {
      if (!this.validateFilter()) {
        return
      }
      this.editObject = null
      this.editParent = scope && scope.row
      this.leftView = 'add'
    },
    onAddType(scope) {
      if (!this.validateFilter()) {
        return
      }
      this.editTypeObject = null
      this.editParent = scope && scope.row
      this.leftView = 'addAccountType'
    },
    onEdit(scope) {
      this.editObject = scope.row
      this.editParent = scope.row.parent
      this.leftView = 'edit'
    },
    onEditType(scope) {
      this.editTypeObject = scope.row
      this.editParent = scope.row.parent
      this.leftView = 'editAccountType'
    },
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const account_id = scope.row.account_id
          return new Promise((resolve, reject) => {
            deleteAccount(account_id)
              .then(res => {
                if (this.editObject && this.editObject.account_id === account_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    onDeleteType(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          this.loading = true
          const fund_id = scope.row.fund_id
          return new Promise((resolve, reject) => {
            deleteFund(fund_id)
              .then(res => {
                if (this.editTypeObject && this.editTypeObject.fund_id === fund_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
              .finally(() => {
                this.loading = false
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
        .catch(() => {})
    },
    fetchData() {
      this.loading = true
      // 加載偏好設置
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.fundtypes = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.years.length > 0) {
            const year = this.years.find(i => i.fy_code === this.preferences.filters.year)
            if (!year) {
              this.preferences.filters.year = this.years[0].fy_code
            }
          }
          if (this.fundtypes.length > 0) {
            const fund = this.fundtypes.find(
              i => i.fund_id === this.preferences.filters.selectedFundType,
            )
            if (!fund) {
              this.preferences.filters.selectedFundType = this.fundtypes[0].fund_id
            }
          }
        })
        .then(() => {
          if (this.preferences.filters.selectedFundType) {
            return getAccountTree(
              this.preferences.filters.selectedFundType,
              this.preferences.filters.isEnableYear ? this.preferences.filters.year : '',
            ) // this.isEnableYear ? this.selectedYear : ''
          } else {
            return Promise.reject()
          }
        })
        .then(res => {
          this.accountTree = res
          this.handleMaxLevel()
          this.$nextTick(() => {
            this.expandLevel(this.preferences.filters.currentLevel)
          })
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    fetchTree() {
      this.leftView = null
      this.editObject = null
      this.editParent = null
      this.editTypeObject = null
      this.loading = true
      getAccountTree(
        this.preferences.filters.selectedFundType,
        this.preferences.filters.isEnableYear ? this.preferences.filters.year : '',
      )
        .then(res => {
          this.accountTree = res
          this.handleMaxLevel()
          this.$nextTick(() => {
            this.expandLevel(this.preferences.filters.currentLevel)
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.fetchTree()
      }
    },
    onExport() {
      if (this.loading) {
        return
      }
      if (!this.validateFilter()) {
        return
      }

      this.loading = true

      exportAccount(this.preferences.filters.selectedFundType)
        .then(res => exportExcel(res, this.exportFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onImport({ results, header }) {
      this.loading = true

      importExcel(this, importAccount, results, header)
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
    onChangeExpanded(listStr) {
      this.preferences.filters.expandedList = listStr
      this.preferences.filters.currentLevel = 0
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .year {
    width: 150px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.tree-table {
  .operation_icon {
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
</style>
