<template>
  <div class="year-form">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      :inline="true"
      label-position="left"
      label-width="50px"
    >
      <el-form-item :label="$t('master.year.label.fy_code')" label-width="auto" prop="fy_code">
        <el-input v-model="form.fy_code" :readonly="true" class="fy-code" size="mini" />
      </el-form-item>
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('master.year.label.fy_name')"
        prop="fy_name"
      >
        <el-input v-model="form.fy_name" :readonly="true" class="fy-name" size="mini" />
      </el-form-item>
      <el-form-item :label="$t('master.year.label.fy_status')" label-width="auto">
        <el-select
          v-model="form.fy_status"
          :placeholder="$t('placeholder.select')"
          size="mini"
          class="fy-status"
          @change="changeStatus"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('master.year.label.fy_enquiry')" label-width="auto">
        <el-checkbox v-model="form.enquiry" />
      </el-form-item>
      <br>
      <el-table
        ref="singleTable"
        :data="form.periods"
        :row-class-name="isStripe"
        max-height="450"
        border
      >
        <el-table-column :label="$t('master.year.yymm')" property="pd_code" width="100" />
        <el-table-column :label="$t('master.year.month')" property="pd_name">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-input
              v-model="scope.row.pd_name"
              :readonly="true"
              class="edit-input"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('master.year.status')"
          align="left"
          width="200"
          property="pd_status"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div class="operation_icon">
              <el-select
                v-model="scope.row.pd_status"
                :placeholder="$t('placeholder.select')"
                size="mini"
                class="table-fy-status"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-form-item label-width="60px" class="actions">
        <el-button :disabled="loading" size="mini" type="primary" @click="onSave">
          {{ editYear ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { createYear, editYear, getYear, fetchYears } from '@/api/master/years'
import { mapGetters, mapActions } from 'vuex'
import { appendZero } from '@/utils'

export default {
  name: 'MasterYearAdd',
  props: {
    editYear: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      form: {
        fy_id: '',
        fy_code: '',
        fy_name: '',
        fy_status: '',
        fy_enquiry: 'Y',
        enquiry: true,
        periods_json: '',
        periods: [],
      },
      defaultForm: {
        fy_id: '',
        fy_code: '',
        fy_name: '',
        fy_status: 'O',
        fy_enquiry: 'Y',
        enquiry: true,
        periods_json: '',
        periods: [],
      },
      loading: false,
    }
  },
  computed: {
    ...mapGetters(['system', 'school']),
    statusOptions() {
      return [
        {
          value: 'O',
          label: this.$t('master.year.option.open'),
        },
        {
          value: 'C',
          label: this.$t('master.year.option.close'),
        },
      ]
    },
  },
  watch: {
    editYear() {
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    ...mapActions([]),
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      // console.log(row)
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    // changed() {
    //   if (!this.loading) {
    //     this.updated()
    //   }
    // },
    changeStatus(status) {
      if (status === 'C') {
        this.form.periods.forEach(item => {
          item.pd_status = 'C'
        })
      }
    },
    generateDate() {
      return new Promise((resolve, reject) => {
        fetchYears()
          .then(res => {
            if (res.length > 0) {
              const fy_code = res[res.length - 1].fy_code
              const arr = []
              if (fy_code) {
                const d = new Date(
                  `${2000 + parseInt(fy_code) + 1}/${this.school.sch_start_month}/01`,
                )
                const code = d.getFullYear().toString().substring(2, 4)
                const name = `${d.getFullYear()}-${(d.getFullYear() + 1)
                  .toString()
                  .substring(2, 4)}`
                for (let i = 0; i < 12; i++) {
                  const year_2 = d.getFullYear().toString().substring(2, 4)
                  const year_4 = d.getFullYear()
                  const month = appendZero(d.getMonth() + 1)
                  arr.push({
                    pd_code: `${year_2}${month}`,
                    pd_name: `${month}/${year_4}`,
                    pd_status: 'O',
                  })
                  d.setMonth(d.getMonth() + 1)
                }
                resolve({
                  name,
                  code,
                  list: arr,
                })
              } else {
                reject(new Error(this.$t('message.historicalYearFieldError')))
                return
              }
            } else {
              reject(new Error(this.$t('message.noCurrentYearRecord')))
            }
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    initData() {
      this.loading = true
      if (this.editYear) {
        // 編輯
        getYear(this.editYear.fy_id)
          .then(res => {
            this.form = Object.assign(this.defaultForm, res)
            this.form.enquiry = this.editYear.fy_enquiry === 'Y'
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        // 新增
        this.form = Object.assign({}, this.defaultForm)
        this.generateDate()
          .then(res => {
            this.form.periods = res.list
            this.form.fy_code = res.code
            this.form.fy_name = res.name
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    formatPeriodsToJson(periods) {
      const newPeriods = periods.map(item => {
        return {
          pd_code: item.pd_code,
          pd_name: item.pd_name,
          pd_status: item.pd_status,
        }
      })
      return JSON.stringify(newPeriods)
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        this.loading = true
        const fy_id = this.form.fy_id
        const fy_name = this.form.fy_name
        const fy_code = this.form.fy_code
        const fy_status = this.form.fy_status
        const fy_enquiry = this.form.enquiry ? 'Y' : 'N'
        const periods_json = this.formatPeriodsToJson(this.form.periods)
        if (this.editYear) {
          // 編輯
          editYear(fy_id, fy_name, fy_status, fy_enquiry, periods_json)
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false
            })
        } else {
          // 新增
          createYear(fy_code, fy_name, fy_status, fy_enquiry, periods_json)
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.year-form {
  label,
  input {
    line-height: 20px;
  }
  .fy-code {
    width: 50px;
  }
  .fy-name {
    width: 100px;
  }
  .fy-status {
    width: 100px;
  }
  .table-fy-status {
    padding: 0 3px;
    * {
      padding: 0 3px;
    }
  }
  .actions {
    margin-top: 10px;
  }
}
</style>

<style rel="stylesheet/scss" lang="scss">
.year-form {
  label,
  input,
  .el-form-item__content,
  i {
    line-height: 25px !important;
    height: 25px !important;
  }
}
.year-form {
  .table-fy-status {
    padding: 0;
    margin: 0;
    input {
      padding: 0 5px;
    }
  }
  .el-table tbody tr td:nth-child(2) .cell {
    padding: 0;
  }
}
</style>
