<template>
  <div ref="coding" class="coding">
    <!-- coding -->
    <div class="msg">
      <el-row>
        <el-col>
          <!--          <img ref="img" src="/static/WorkInProgress.jpg" alt="未完成">-->

          <svg-icon class="working" icon-class="working" />
          <span class="tips">{{ $t('message.workInProgress') }}</span>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import loadPreferences from '@/views/mixins/loadPreferences'
export default {
  name: 'Coding',
  mixins: [loadPreferences],
  mounted() {
    // this.$nextTick(() => {
    //   this.$refs.img.ondragstart = function(e) {
    //     e.preventDefault()
    //   }
    // })
    this.saveUserLastPage()
  },
}
</script>

<style lang="scss" scoped>
.coding {
  display: inline-grid;
  color: #707070;
  font-size: 25px;
  width: 100%;
  height: 95%;
  .msg {
    margin: auto;
    .working {
      pointer-events: none;
      display: block;
      margin: auto;
      font-size: 14em;
      width: 200px;
      height: 200px;
    }
    .tips {
      display: block;
      width: 100%;
      margin: 50px auto;
      text-align: center;
      font-size: 2em;
    }
  }
}
</style>
