<template>
  <div>
    <div class="actions">
      <!--      <el-button type="primary" size="mini" class="new-row" @click="onAddRow">{{ $t('button.add') }}</el-button>-->
      <el-button
        type="primary"
        size="mini"
        class="new-row"
        icon="el-icon-plus"
        circle
        @click="onAddRow"
      />
    </div>
    <el-table :show-header="false" :data="selectedStaffs" v-bind="$attrs" style="width: 100%">
      <el-table-column :label="$t('table.index')" type="index" />
      <el-table-column :label="$t('table.staff')" width="180">
        <template v-if="scope && scope.row" slot-scope="scope">
          <el-select v-model="scope.row.staff_id">
            <el-option
              v-for="item in staffs"
              :key="item.staff_id"
              :value="item.staff_id"
              :label="`[${item.st_code}] ${item[language === 'en' ? 'st_name_en' : 'st_name_cn']}`"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.type')" width="120">
        <template v-if="scope && scope.row" slot-scope="scope">
          <el-select v-if="showType" v-model="scope.row.type">
            <el-option :label="$t('budget.budgetSet.label.memberTypeE')" value="E" />
            <el-option :label="$t('budget.budgetSet.label.memberTypeN')" value="N" />
            <el-option :label="$t('budget.budgetSet.label.memberTypeL')" value="L" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.action')" min-width="80">
        <template v-if="scope && scope.row" slot-scope="scope">
          <span>
            <i class="el-icon-delete" @click="removeRow(scope.row)" />
          </span>
        </template>
      </el-table-column>
      <!--        <template slot="empty">-->
      <!--          <span></span>-->
      <!--          <div class="bottom-action">-->
      <!--            <el-button type="primary" size="mini" class="new-row" @click="onAddRow">add</el-button>-->
      <!--  &lt;!&ndash;          <i class="el-icon-plus" @click="onAddRow"/>&ndash;&gt;-->
      <!--          </div>-->
      <!--        </template>-->
      <!--      <template slot="append">-->
      <!--        <div class="bottom-action">-->
      <!--          <el-button type="primary" size="mini" class="new-row" @click="onAddRow">add</el-button>-->
      <!--&lt;!&ndash;          <i class="el-icon-plus" @click="onAddRow"/>&ndash;&gt;-->
      <!--        </div>-->
      <!--      </template>-->
    </el-table>
  </div>
</template>

<script>
import { searchStaffs } from '@/api/assistance/staff'
import { mapGetters } from 'vuex'

export default {
  name: 'BudgetMember',
  props: {
    staffList: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
    fyCode: {
      type: String,
      required: true,
    },
    showType: {
      type: Boolean,
      default: true,
    },
    st_grade: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      staffs: [],
    }
  },
  computed: {
    ...mapGetters(['language']),
    selectedStaffs: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
  },
  created() {
    const fy_code = this.fyCode
    const st_grade = this.st_grade
    searchStaffs({ fy_code, st_grade }).then(res => {
      this.staffs = res
    })
  },
  methods: {
    onAddRow() {
      const item = {
        staff_id: '',
      }
      item.type = this.showType ? 'E' : 'M'
      this.selectedStaffs.push(item)
    },
    removeRow(row) {
      const data_index = this.selectedStaffs.findIndex(i => i.staff_id === row.staff_id)
      data_index > -1 && this.selectedStaffs.splice(data_index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.actions {
  text-align: right;
  float: right;
  z-index: 1;
  position: absolute;
  right: 0;
  .new-row {
  }
}
.el-icon-delete {
  cursor: pointer;
}
.bottom-action {
  text-align: center;
  .new-row {
  }
}
/deep/ {
  .el-table__empty-block {
    min-height: 35px;
  }
}
</style>
