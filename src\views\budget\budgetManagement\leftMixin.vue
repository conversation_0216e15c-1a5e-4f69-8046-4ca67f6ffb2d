<script>
import { listenTo } from '@/utils/resizeListen'
import {
  cancelScrollBarSync,
  scrollBarSync,
  getTreeBudgetByCode,
  getTreeBudgetById,
} from '@/views/budget/common/utils'
import { editBudgetStage } from '@/api/budget'

export default {
  props: {},
  data() {
    return {
      leftResizeListen: {},
      leftInfoHeight: 70,
      leftDataTableWrapper: {},
      leftSummaryTableWrapper: {},
      showRightTable: true,
      leftColumnWidths: {
        icon: 120,
        budget_code: 120,
        name_: 150,
        budget_IE: 50,
        budget_amount: 100,
        actual_amount: 100,
      },
      leftScope: {},
    }
  },
  computed: {
    leftTableHeight() {
      // return `height: calc(100% - ${this.summary.budget_IE === 'B' ? 113 : 90}px);`
      return `height: calc(100% - ${this.leftInfoHeight}px - 30px);`
    },
    fyCode() {
      return this.preferences.filters.selectedYear
    },
  },
  created() {},
  beforeDestroy() {
    // cancelScrollBarSync(this.summaryTableWrapper, this.dataTableWrapper)
    this.leftCancelScrollBarSync()
  },
  mounted() {
    this.leftScrollBarSync()
    // this.$nextTick(() => {
    //   this.leftResizeListen = listenTo(this.$refs.form.$el, ({ width, height, ele }) => {
    //     this.leftInfoHeight = height + 10
    //   })
    // })
    this.leftInfoHeight = 0
  },
  updated() {},
  methods: {
    leftScrollBarSync() {
      this.$nextTick(() => {
        try {
          this.leftDataTableWrapper =
            this.$refs['leftDataTable'].$el.querySelector('.el-table__body-wrapper')
          this.leftSummaryTableWrapper =
            this.$refs['leftSummaryTable'].$el.querySelector('.el-table__body-wrapper')
          scrollBarSync(this.leftDataTableWrapper, this.leftSummaryTableWrapper)
        } catch (e) {
          // error
        }
      })
    },
    leftCancelScrollBarSync() {
      this.$nextTick(() => {
        cancelScrollBarSync(this.leftDataTableWrapper, this.leftSummaryTableWrapper)
      })
    },
    leftSummarySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3,
        }
      } else if (columnIndex < 3) {
        return {
          rowspan: 0,
          colspan: 0,
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1,
        }
      }
    },
    leftGetSummaries({ columns, data }) {
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('budget.budgetManagement.label.total')
          return
        } else if (index === 1) {
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          // sums[index] = 'N/A'
        }
      })

      return sums
    },
    onFold() {
      this.$emit('onFold')
    },
    onUnfold() {
      this.$emit('onUnfold')
    },
    // 點擊行
    onLeftRowClick(row, column, event) {
      if (this.leftScope.row && this.leftScope.row.budget_id === row.budget_id) {
        this.clearLeftSelect()
        const budgetByCodeData = getTreeBudgetById(
          this.budgetGroupList,
          this.preferences.filters.selectedGroupId,
        )
        let budgetByIdData
        if (budgetByCodeData && budgetByCodeData.budget_id) {
          budgetByIdData = getTreeBudgetByCode(this.bottomBudgetTree, budgetByCodeData.budget_code)
        }
        if (budgetByIdData && budgetByIdData.budget_id) {
          this.changeBottomList(this.lastFyCode, budgetByIdData.budget_id, this.lastGroup)
        }
        return
      }
      this.clearRightSelect()
      this.leftScope = { row }
      this.changeBottomList(this.lastFyCode, row.budget_id, this.lastGroup)
    },
    clearLeftSelect() {
      this.leftScope = {}
      this.rightScope = {}
      this.$refs.leftDataTable.setCurrentRow()
    },
  },
}
</script>
