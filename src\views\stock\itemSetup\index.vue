<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <!-- 會計週期 -->
        <el-select
          v-model="preferences.filters.year"
          class="year"
          style="width: 110px"
          @change="fetchTree"
        >
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>

        <svg-icon
          v-if="hasPermission_Add"
          :title="$t('btnTitle.addStockGroup')"
          icon-class="new_folder"
          @click="onAddType"
        />
        <svg-icon
          v-if="hasPermission_Add"
          :title="$t('btnTitle.add')"
          icon-class="new_file"
          @click="onAdd"
        />
        <div v-for="i in stockLevel" :key="i" class="selectLevel" @click="expandLevel(i)">
          {{ i }}
        </div>
        <div class="selectLevel" @click="enableYear">
          {{ preferences.filters.isEnableYear ? $t('common.yes') : $t('common.no') }}
        </div>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 上傳 -->
          <div v-if="hasPermission_Input">
            <el-dropdown>
              <svg-icon icon-class="import" class="action-icon" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="importDialog = true">
                  {{ $t('stock.itemSetup.button.goodsImport') }}
                </el-dropdown-item>
                <el-dropdown-item @click.native="importPriceDialog = true">
                  {{ $t('stock.itemSetup.button.priceImport') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <!-- 下載 -->
          <div v-if="hasPermission_Output" class="output">
            <el-dropdown>
              <svg-icon icon-class="export" class="action-icon" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="onExport">
                  {{ $t('stock.itemSetup.button.goodsExport') }}
                </el-dropdown-item>
                <el-dropdown-item @click.native="onExportPrice">
                  {{ $t('stock.itemSetup.button.priceExport') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <tree-table
          ref="TreeTable"
          :data="stockTree"
          :eval-func="func"
          :eval-args="args"
          :expand-all="true"
          :first-field="language === 'en' ? 'name_en' : 'name_cn'"
          :first-field-align="firstFieldAlign"
          :first-field-width="firstFieldWidth"
          number-field="code"
          folder-field="stock_group_id"
          border
          @changeWidth="changeColumnWidth"
          @changeExpanded="onChangeExpanded"
        >
          <!--    編號姓名      -->
          <!-- <template v-if="scope && scope.row" slot-scope="{ scope }" slot="firstField">
            <span v-if="!scope.row.stock_group_id" :class="'number-field' + (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year) ? ' selected' : '')">{{ scope.row.code }}</span>
            <span class="key">{{ scope.row[language === 'en' ? 'name_en' : 'name_cn'] }}</span>
          </template> -->
          <template v-if="scope && scope.row" slot="firstField" slot-scope="{ scope }">
            <i
              v-if="scope.row.stock_group_id"
              :class="
                'edac-icon edac-icon-folder' +
                  (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year)
                    ? ' activate'
                    : '')
              "
            />
            <i v-else class="edac-icon edac-icon-file" />
            <span
              v-if="!scope.row.stock_group_id"
              :class="
                'number-field' +
                  (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year)
                    ? ' selected'
                    : '')
              "
            >{{ scope.row.code }}</span>
            <span class="key">{{ scope.row[language === 'en' ? 'name_en' : 'name_cn'] }}</span>
          </template>
          <!-- 其餘字段 -->
          <el-table-column
            v-for="item in filteredStyleColumns"
            :key="item.ss_id"
            :label="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :width="item.width"
            :property="item.ss_key"
            :column-key="item.ss_key"
            :formatter="formatter"
          />
          <!--操作列-->
          <el-table-column
            :label="$t('table.action')"
            align="left"
            header-align="left"
            min-width="170"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <div v-if="scope.row.stock_group_id" class="operation_icon">
                <svg-icon
                  v-if="hasPermission_Add"
                  icon-class="new_folder"
                  @click="onAddType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <svg-icon v-if="hasPermission_Add" icon-class="new_file" @click="onAdd(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditType(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i
                  v-if="
                    hasPermission_Delete && (!scope.row.children || scope.row.children.length === 0)
                  "
                  class="el-icon-close"
                  @click="onDeleteType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
              </div>
              <div v-else class="operation_icon">
                <svg-icon class="no-cursor" icon-class="" />
                <svg-icon class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
                <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
              </div>
            </template>
          </el-table-column>
        </tree-table>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-object="editObject"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        @onCancel="onViewCancel"
      />
      <addTypePage
        v-else-if="leftView === 'addStockGroup' || leftView === 'editStockGroup'"
        :edit-object="editTypeObject"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      table-type="tree"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      width="450px"
      class="dialog"
    >
      <UploadExcel :on-success="onImport" :on-template="onShowSetting" />
    </el-dialog>
    <!-- importPrice 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importPriceDialog"
      width="450px"
      class="dialog"
    >
      <UploadExcel :on-success="onImportPrice" :on-template="onShowSetting" />
    </el-dialog>
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import addPage from './add'
import addTypePage from './addType'
import {
  deleteStock,
  exportStocks,
  exportStocksPrice,
  getStocksTree,
  importStocks,
  importStocksPrice,
} from '@/api/stock/itemSetup'
import { deleteStockGroup } from '@/api/stock/itemSetup/itemSetupGroup'
import { mapGetters } from 'vuex'
import treeToArray from '@/components/TreeTable/eval.js'
import treeTable from '@/components/TreeTable'
import UploadExcel from '@/components/UploadExcel/index'
import { fetchYears } from '@/api/master/years'

import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'

import mixinPermission from '@/views/mixins/permission'
import loadPreferences from '@/views/mixins/loadPreferences'

import { exportExcel, importExcel } from '@/utils/excel'

import { amountFormat } from '@/utils'
import dateUtil from '@/utils/date'

export default {
  name: 'StockItemSetupIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    addTypePage,
    treeTable,
    UploadExcel,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      leftView: '',
      editObject: null,
      editTypeObject: null,
      func: treeToArray,
      expandAll: false,
      editParent: null,
      args: [null, null, 'timeLine'],
      stockTree: [],
      years: [],
      selectedYear: '',
      stockLevel: 0,
      currentLevel: 99,
      isEnableYear: false,
      importDialog: false,
      importPriceDialog: false,
      loading: false,
      exportFileName: 'stocks',
      exportStocksFileName: 'items',
      exportStocksPriceFileName: 'item_prices',

      langKey: 'stock.itemSetup.label.',
      tableColumns: ['name_cn', 'name_en', 'purchase_price', 'sales_price'],
      amountColumns: ['purchase_price', 'sales_price'],

      preferences: {
        filters: {
          year: '',
          currentLevel: 99,
          isEnableYear: false,
          expandedList: '',
        },
      },
    }
  },
  computed: {
    ...mapGetters(['language']),
    filteredStyleColumns() {
      return this.styleColumns.filter(item => item.ss_key !== 'first_field')
    },
  },
  created() {},
  mounted() {
    this.fetchData()
  },
  methods: {
    expandLevel(level) {
      if (level) {
        this.$refs.TreeTable.showLevel(level)
        this.preferences.filters.currentLevel = level
        this.preferences.filters.expandedList = ''
      } else {
        this.$refs.TreeTable.setExpandItem(this.preferences.filters.expandedList)
      }
    },
    handleMaxLevel() {
      this.$nextTick(() => {
        this.stockLevel = this.$refs.TreeTable.getMaxLevel()
      })
    },
    enableYear() {
      this.preferences.filters.isEnableYear = !this.preferences.filters.isEnableYear
      this.fetchTree()
    },
    onAdd(scope) {
      this.editObject = null
      this.editParent = scope && scope.row
      this.leftView = 'add'
    },
    onAddType(scope) {
      this.editTypeObject = null
      this.editParent = scope && scope.row
      this.leftView = 'addStockGroup'
    },
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'edit'
    },
    onEditType(scope) {
      this.editTypeObject = scope.row
      this.leftView = 'editStockGroup'
    },
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const stock_id = scope.row.stock_id
          return new Promise((resolve, reject) => {
            deleteStock(stock_id)
              .then(res => {
                if (this.editObject && this.editObject.stock_id === stock_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    onDeleteType(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const stock_group_id = scope.row.stock_group_id
          return new Promise((resolve, reject) => {
            deleteStockGroup(stock_group_id)
              .then(res => {
                if (this.editTypeObject && this.editTypeObject.stock_group_id === stock_group_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.years.length) {
            const year = this.years.find(i => i.fy_code === this.preferences.filters.year)
            if (year) {
              return
            }
            this.preferences.filters.year = this.years[0].fy_code
          } else {
            return Promise.reject(this.$t('message.theYearDoNotExist'))
          }
        })
        .then(this.updateChildPreference)
        .then(() => {
          return getStocksTree(
            this.preferences.filters.year,
            this.preferences.filters.isEnableYear ? 'N' : 'Y',
          )
        })
        .then(res => {
          this.stockTree = res
          this.handleMaxLevel()
          this.$nextTick(() => {
            this.expandLevel(this.preferences.filters.currentLevel)
          })
        })
        .catch(err => {
          this.$message.err(err)
        })
    },
    fetchTree() {
      this.leftView = null
      this.editObject = null
      this.editParent = null
      this.editTypeObject = null
      getStocksTree(
        this.preferences.filters.year,
        this.preferences.filters.isEnableYear ? 'N' : 'Y',
      ).then(res => {
        this.stockTree = res
        this.handleMaxLevel()
        this.$nextTick(() => {
          this.expandLevel(this.preferences.filters.currentLevel)
        })
      })
    },
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.fetchTree()
      }
    },
    onExport() {
      if (this.loading) {
        return
      }
      this.loading = true
      exportStocks()
        .then(res => exportExcel(res, this.exportStocksFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onExportPrice() {
      if (this.loading) {
        return
      }
      this.loading = true
      exportStocksPrice()
        .then(res => exportExcel(res, this.exportStocksPriceFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onImport({ results, header }) {
      this.loading = true
      importExcel(this, importStocks, results, header)
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
    onImportPrice({ results, header }) {
      this.loading = true
      importExcel(this, importStocksPrice, results, header)
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importPriceDialog = false
        })
    },
    onChangeExpanded(listStr) {
      this.preferences.filters.expandedList = listStr
      this.preferences.filters.currentLevel = 0
    },
    formatter(row, column, cellValue, index) {
      if (cellValue == null || cellValue === '') return ''
      if (this.amountColumns.includes(column.property)) {
        // 金額
        return amountFormat(cellValue)
      }
      if (column.property.includes('date')) {
        // 日期
        return dateUtil.format(new Date(cellValue), 'MM/dd/yyyy')
      }
      return cellValue
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.itemBtn {
  padding: 0;
  border: 0;
}
.output {
  margin-left: 0;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .year {
    width: 100px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.output {
}
.tree-table {
  .operation_icon {
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
.dialog {
  .el-dialog__body {
    height: auto !important;
  }
}
</style>
