<template>
  <div ref="page" class="payment-info">
    <!--第一行-->
    <div ref="actions" class="actions">
      <el-form ref="noForm" :model="form">
        <el-form-item
          :rules="[
            {
              required: true,
              message: ' ',
            },
          ]"
          prop="vc_no"
        >
          <el-input v-if="action === 'edit'" v-model="current_vc_no" readonly />
          <i class="action-icon el-icon-arrow-left" @click="onPrevVoucher" />
          <el-input v-model="form.vc_no">
            <i slot="suffix" class="edac-icon action-icon edac-icon-search" @click="onSelectNo" />
          </el-input>
          <i class="action-icon el-icon-arrow-right" @click="onNextVoucher" />
          <div class="view-button" style="display: inline">
            <el-button type="primary" size="mini" @click="onAddRow">
              {{ $t('daily.voucher.button.addRow') }}
            </el-button>
            <el-button type="primary" size="mini" @click="onDeleteRow">
              {{ $t('daily.voucher.button.deleteRow') }}
            </el-button>
            <el-button type="primary" size="mini" @click="onSave">
              {{ $t('daily.voucher.button.save') }}
            </el-button>
            <el-button type="primary" size="mini" @click="onCancel">
              {{ $t('daily.voucher.button.cancel') }}
            </el-button>
            <i
              v-if="hasPermission_Output"
              class="edac-icon action-icon edac-icon-export"
              style="float: right"
              @click="onExport"
            />
            <i
              class="edac-icon action-icon edac-icon-import"
              style="float: right"
              @click="onImport"
            />
            <i
              v-if="action === 'add'"
              class="edac-icon action-icon edac-icon-setting1"
              style="float: right; color: #b9b6b6; margin-right: 10px"
              @click="onShowSetting"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <Summary
      v-if="fy_code"
      ref="summary"
      :is-view="isView"
      :data.sync="form"
      :table-data.sync="tableData"
      :action="action === 'edit' ? 'edit' : 'add'"
      :dialog-no-list-visible.sync="dialogNoListVisible"
      :old-amount="oldAmount"
      :old-dr-amount="oldDrAmount"
      :old-cr-amount="oldCrAmount"
      @changeCommand="changeCommand"
    />

    <VTable
      v-if="form && form.fy_code && form.fund_id"
      ref="DTable"
      :is-view="isView"
      :fy_code="form.fy_code"
      :data.sync="tableData"
      :action="action === 'edit' ? 'edit' : 'add'"
      :info="form"
      :detail-styles="detailStyles"
      :max-height="pageHeight - actionsHeight - summaryHeight - 10"
      :current-date="form.vc_date"
      :vt_category="vt_category"
      @handleDescCopyUp="handleDescCopyUp"
    />
    <!-- 選擇傳票編號 -->
    <!--    <el-dialog :visible.sync="dialogNoListVisible" title="傳票編號">-->
    <!--      <el-row :gutter="10">-->
    <!--        <el-col-->
    <!--          v-for="item in noList"-->
    <!--          :key="item"-->
    <!--          :xs="4"-->
    <!--          :sm="6"-->
    <!--          :md="8"-->
    <!--          :lg="9"-->
    <!--          :xl="11"-->
    <!--        >-->
    <!--          <div class="grid-content bg-purple">{{ item }}</div>-->
    <!--        </el-col>-->
    <!--      </el-row>-->
    <!--    </el-dialog>-->
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @close="onCloseCustomStyleDialog"
    />

    <!-- import 對話框 -->
    <el-dialog
      v-loading="importLoading"
      :title="$t('file.excelImport')"
      :visible.sync="importVisible"
      width="450px"
      class="upload-dialog"
    >
      <UploadExcel :on-success="handleImport" :raw="true" />
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import Summary from '@/views/daily/components/summary'
import VTable from '@/views/daily/components/VTable'
import UploadExcel from '@/components/UploadExcel/index'
// 樣式
import customStyle from './detailStyle'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'

import { fetchYears, searchByDate } from '@/api/master/years'

import {
  createVoucher,
  editVoucher,
  deleteVoucher,
  getVoucher,
  exportVoucher,
  importVoucher,
} from '@/api/daily/payment'
import { getCycleDate } from '@/api/getCycleDate'
import { exportExcel, importExcel } from '@/utils/excel'
import { fetchDescriptions, getDescriptions } from '@/api/assistance/description'
import { searchCompanies } from '@/api/assistance/payeePayer'
import { listenTo } from '@/utils/resizeListen'
import BigJs from 'big.js'
// import { deepCloneByJSON } from '@/utils'

export default {
  name: 'DailyAdd',
  components: { VBreadCrumb, Summary, VTable, customStyle, UploadExcel },
  mixins: [mixinPermission, loadCustomStyle],
  props: {
    vt_category: {
      type: String,
      required: true,
    },
    isView: {
      type: Boolean,
      default: false,
    },
    editObject: {
      type: Object,
      default: null,
    },
    parentFyCode: {
      type: String,
      default: '',
    },
    action: {
      type: String,
      default: 'add',
    },
    vt_code: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      current_vc_no: '',
      tableData: [],
      currentYear: null,
      langKey: 'daily.voucher.label.',
      fy_code: '',
      form: {
        vc_id: 0,
        vc_no: '',
        vt_category: this.vt_category,
        vt_code: this.vt_code,
        pd_code: '',
        vc_date: new Date(),
        vc_summary: '',
        vc_amount: '0.00',
        vc_method: 'TRANSF',
        ref: '',
        vc_chq_date: '',
        vc_payee: '',
        vc_st_code: '',
        vc_extra: 'N',
        vc_receipt: this.vt_category === 'R' ? '' : '',
        vc_receipt_date: null,
        vc_status: 1,
        vc_chq_status: 1,
        ledgers: [],
      },
      defaultItem: {
        ac_code: '',
        account_id: '',
        descr: '',
        tx_type: 'I',
        amount_dr: '0.00',
        amount_cr: '0.00',
        select_amount_type: 'Dr',
        vc_payee: '',
        ref: '',
        budget_code: null,
        st_code: null,
        vc_contra: null,
        vc_dept: null,
        vc_quote: '',
        vc_qnum: null,
      },
      from: null,

      // ss_code: 'ac.daily.detail',
      dialogNoListVisible: false,
      manualLoadStyle: true,
      importVisible: false,
      importLoading: false,

      actionsResizeListen: {},
      actionsHeight: 30,
      summaryResizeListen: {},
      summaryHeight: 0,
      pageResizeListen: {},
      pageHeight: 500,
      oldAmount: 0,
      oldDrAmount: 0,
      oldCrAmount: 0,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'currentDate']),
    fundId() {
      if (this.voucherList.length && this.info.vt_code) {
        const fund = this.voucherList.find(i => i.vt_code === this.info.vt_code)
        if (fund) {
          return fund.fund_id
        }
      }
      return ''
    },
    detailStyles() {
      const data = {}
      this.styleData.forEach(item => {
        data[item.ss_key] = item.value === '1'
      })
      return data
    },
    ss_code() {
      return this.$route.meta.p_code + '.detail'
    },
  },
  watch: {
    // 'form.vc_date'(val) {
    //   // this.handleYear()
    //   console.log('vc_date', val)
    // },
    editObject() {
      this.initData()
    },
    isView() {
      this.initData()
    },
    currentDate(val) {
      this.form.vc_date = val
    },
  },
  beforeRouteEnter(to, from, next) {
    if (to.params.vt_category) {
      next(vm => {
        vm.from = from
      })
    } else {
      next(to.matched[1])
    }
  },
  created() {
    this.autoLoadStyle = false
    // if (!this.vt_category) {
    //   this.$router.push({ name: 'dailyPaymentList' })
    // }
    this.initData().then(this.handleYear)
  },
  mounted() {
    // this.handleYear()
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.actionsResizeListen = listenTo(this.$refs.actions, ({ width, height, ele }) => {
        this.actionsHeight = height
        if (this.$refs.summary) {
          this.summaryHeight = this.$refs.summary.$el.offsetHeight
        }
      })
    })
  },
  methods: {
    initData() {
      return new Promise(async(resolve, reject) => {
        if (this.editObject) {
          // edit or copy
          getVoucher({
            fy_code: this.parentFyCode,
            vc_id: this.editObject.vc_id,
          })
            .then(res => {
              // action = 'copy' 或 action = 'reverse_copy' 時，是 複製操作
              if (this.action === 'copy') {
                res.vc_id = ''
                res.ref = ''
                res.vc_no = ''
                // 右上角輸入的日期
                res.vc_date = this.currentDate
                res.vc_receipt_date !== '' ? (res.vc_receipt_date = res.vc_date) : ''
              } else if (this.action === 'reverse_copy') {
                res.vc_id = ''
                res.vc_no = ''
                res.vt_code = this.editObject.vt_code
                res.fund_id = this.editObject.fund_id
                res.vt_category = this.editObject.vt_category
                // 右上角輸入的日期
                res.vc_date = this.currentDate
                res.vc_receipt_date !== '' ? (res.vc_receipt_date = res.vc_date) : ''

                this.vt_category === 'R' ? (res.vc_receipt = '') : ''

                // 反向複製時，銀行轉賬 和 一般傳票 Dr 和 Cr 互調
                res.ledgers.forEach((val, index, target) => {
                  const tmpCr = val.amount_cr
                  target[index].amount_cr = val.amount_dr
                  target[index].amount_dr = tmpCr
                })
              }
              this.form = res
              this.oldAmount = res.vc_amount
              this.tableData = res.ledgers
              const oldAmount = res.ledgers.reduce((acc, curr) => {
                return acc.plus(curr.amount_dr).plus(curr.amount_cr)
              }, new BigJs(0))
              this.oldAmount = Number(oldAmount.toString())
              this.oldDrAmount = Number(
                res.ledgers
                  .reduce((acc, curr) => {
                    return acc.plus(curr.amount_dr)
                  }, new BigJs(0))
                  .toString(),
              )
              this.oldCrAmount = Number(
                res.ledgers
                  .reduce((acc, curr) => {
                    return acc.plus(curr.amount_cr)
                  }, new BigJs(0))
                  .toString(),
              )
              this.current_vc_no = res.vc_no

              // this.checkDesc()
              // this.checkPayee()
            })
            .then(this.checkDesc)
            .then(this.checkPayee)
            .finally(() => {
              resolve()
            })
        } else {
          await this.loadUserStyle()
          // add
          this.tableData = [Object.assign({}, this.defaultItem)]
          if (this.vt_category === 'T' || this.vt_category === 'J') {
            const drItem = Object.assign({}, this.defaultItem)
            drItem.select_amount_type = 'Cr'
            this.tableData.push(drItem)
          }

          const keys = Object.keys(this.detailStyles)
          for (let i = 0; i < keys.length; i++) {
            const key = keys[i]
            this.tableData[0][key] = this.detailStyles[key]
          }

          // 右上角輸入的日期
          this.form.vc_date = this.currentDate

          // 默認形式
          switch (this.vt_category) {
            case 'P':
              this.form.vc_method = 'CHEQUE'
              this.form.vc_chq_date = this.form.vc_date
              break
            case 'C':
              this.form.vc_method = 'CASH'
              break
          }
          resolve()
        }
      })
    },
    async checkDesc() {
      const data = this.tableData
      const fy_code = this.fy_code
      try {
        const full_desc = this.form.vc_summary + ''
        const res = await fetchDescriptions({ full_desc })
        this.$set(this.form, '_descIsNew', !res.some(i => i.comp_name === full_desc))
      } catch (e) {
        console.error(e)
      }
      const checkList = []
      for (let i = 0; i < data.length; i++) {
        const row = data[i]
        const full_desc = row.descr + ''
        if (full_desc.replace(/ /g, '').length === 0) {
          break
        }
        const itemIndex = checkList.findIndex(i => i.full_desc === full_desc)
        if (itemIndex === -1) {
          checkList.push({ full_desc, indexs: [i] })
        } else {
          checkList[itemIndex].indexs.push(i)
        }
        // const res = await fetchDescriptions({ full_desc })
        // this.$set(data[i], 'desc_is_new', !res || res.length === 0)
      }
      try {
        const res = await getDescriptions(
          checkList.map(i => {
            return { full_desc: i.full_desc, fy_code }
          }),
        )
        console.log(res)
        if (res.length === checkList.length) {
          checkList.forEach((item, index) => {
            const isNew = res[index].length === 0
            item.indexs.forEach(i => {
              this.$set(data[i], 'desc_is_new', isNew)
            })
          })
        }
      } catch (e) {
        console.error(e)
      }
    },
    async checkPayee() {
      const payee = this.form.vc_payee
      if (!payee || payee.replace(/ /g, '').length === 0) {
        return
      }
      try {
        const res = await searchCompanies({ name: payee })
        res.forEach(i => {
          i.value = i.comp_name
        })
        // this.form._payeeIsNew = !res.some(i => i.comp_name === payee)
        this.$set(this.form, '_payeeIsNew', !res.some(i => i.comp_name === payee))
      } catch (e) {
        console.error(e)
      }
    },
    // fetchNoData() {
    //   if (!this.form.vc_date) return Promise.reject('未選擇傳票日期')
    //   return new Promise((resolve, reject) => {
    //     fetchNoList({
    //       voucher_type_id: this.form.voucher_type_id,
    //       date: this.form.vc_date,
    //       vc_no: this.form.vc_no ? this.form.vc_no : undefined
    //     })
    //       .then(res => {
    //         this.noList = res
    //         resolve(res)
    //       }).catch(err => {
    //         reject(err)
    //       })
    //   })
    // },
    handleYear() {
      if (!this.form.vc_date) return
      const date = this.dateFormatToStr(new Date(this.form.vc_date))
      searchByDate(date)
        .then(async res => {
          if (res.length === 0) {
            try {
              const years = await fetchYears()
              if (years.length > 0) {
                const year = years[0]
                getCycleDate({ fy_code: year.fy_code })
                  .then(res => {
                    if (res && res.the_first_day) {
                      this.form.vc_date = res.the_first_day
                      this.$set(this.form, 'vc_date', res.the_first_day)
                      if (this.vt_category === 'P') {
                        this.form.vc_chq_date = this.form.vc_date
                        this.$set(this.form, 'vc_chq_date', res.the_first_day)
                      }
                    }
                    this.$forceUpdate()
                  })
                  .catch(e => {
                    console.error(e)
                  })
                  .finally(() => {
                    this.currentYear = year
                    this.fy_code = year.fy_code
                    this.$set(this.form, 'fy_code', year.fy_code)
                  })
              } else {
                this.$message.error(this.$t('message.uninitializedYear'))
              }
            } catch (e) {
              console.error(e)
            }
          } else {
            this.currentYear = res
            this.fy_code = res.fy_code
            this.$set(this.form, 'fy_code', res.fy_code)
          }
        })
        .catch(() => {})
    },
    handleDescCopyUp(desc) {
      this.$set(this.form, 'vc_summary', desc)
    },
    onAddRow() {
      this.$refs.DTable.addRow()
    },
    onDeleteRow() {
      this.$refs.DTable.deleteRow()
    },
    noFormValidate() {
      return new Promise((resolve, reject) => {
        this.$refs.noForm.validate((valid, data) => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    onSave() {
      this.noFormValidate()
        .then(this.$refs.summary.validate)
        .then(() => {
          const fy_code = this.parentFyCode
          const vc_id = this.form.vc_id
          const vt_category = this.vt_category
          const vc_no = this.form.vc_no
          const vt_code = this.form.vt_code
          const vc_date = this.form.vc_date
          const vc_summary = this.form.vc_summary
          const vc_method = this.form.vc_method
          const ref = vc_method === 'CHEQUE' || vc_method === 'OTHER' ? this.form.ref : ''
          const vc_chq_date = vc_method === 'CHEQUE' ? this.form.vc_chq_date : ''
          const vc_payee = this.form.vc_payee
          const vc_st_code = this.form.vc_st_code
          const vc_extra = this.form.vc_extra
          const vc_receipt = this.form.vc_receipt
          const ledgers_json = this.tableData
          const vc_status = this.form.vc_status
          if (vc_no == null || !vc_no) {
            this.$message.error(this.$t('daily.voucher.message.inputVoucherNo'))
            return
          }
          if (!ledgers_json || ledgers_json.length === 0) {
            this.$message.error(this.$t('daily.voucher.message.inputVoucherInfo'))
            return
          }
          for (let i = 0; i < ledgers_json.length; i++) {
            const item = ledgers_json[i]
            if (
              item.ac_code == null ||
              !item.ac_code ||
              item.account_id == null ||
              !item.account_id
            ) {
              this.$message.error(this.$t('daily.voucher.message.inputAccountingSubject'))
              return
            }
            if (item.ac_is_error) {
              this.$message.error(this.$t('daily.voucher.message.invalidAcCode'))
              return
            }
            if (item.staff_is_error) {
              this.$message.error(this.$t('daily.voucher.message.invalidStaff'))
              return
            }
            if (item.dept_is_error) {
              this.$message.error(this.$t('daily.voucher.message.invalidDept'))
              return
            }
            if (item.contra_is_error) {
              this.$message.error(this.$t('daily.voucher.message.invalidContra'))
              return
            }
            // if (parseFloat(item.amount_dr) === 0 && parseFloat(item.amount_cr) === 0) {
            //   this.$message.error(this.$t('daily.voucher.message.theAmountCannotBeZero'))
            //   return
            // }
          }

          if (this.editObject && this.action === 'edit') {
            editVoucher({
              fy_code,
              vt_category,
              vc_id,
              vc_no,
              vt_code,
              vc_date,
              vc_summary,
              vc_method,
              ref,
              vc_chq_date,
              vc_payee,
              vc_st_code,
              vc_extra,
              vc_receipt,
              ledgers_json,
              vc_status,
            }).then(res => {
              this.$message.success(this.$t('message.editSuccess'))
              this.$emit('onCancel', true)
              this.handleReloadAllEnquiryPage()
              this.gotoList()
            })
          } else {
            // add
            createVoucher({
              vc_no,
              vt_category,
              vt_code,
              vc_date,
              vc_summary,
              vc_method,
              ref,
              vc_chq_date,
              vc_payee,
              vc_st_code,
              vc_extra,
              vc_receipt,
              ledgers_json,
              vc_status,
            }).then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
              this.handleReloadAllEnquiryPage()
              this.gotoList()
            })
          }
        })
        .catch(() => {})
    },
    onCancel() {
      if (this.from && this.from.params && this.from.params.action === 'view') {
        const params = Object.assign(this.from.params)
        params.editObject = this.editObject
        this.$router.push({
          name: this.from.name,
          params,
        })
      } else {
        this.$router.back()
      }
    },
    onEdit() {
      this.$emit('handleEdit')
    },
    onDelete() {
      const fy_code = this.fy_code
      const vc_id = this.form.vc_id
      const vc_no = this.form.vc_no
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${vc_no}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          return new Promise((resolve, reject) => {
            deleteVoucher({ fy_code, vc_id })
              .then(res => {
                // this.loadTableData()
                this.gotoList()
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    gotoList() {
      this.$router.push(this.$route.matched[1])
    },
    onSelectNo() {
      this.dialogNoListVisible = true
    },
    onPrevVoucher() {
      if (this.$refs.summary) {
        this.$refs.summary.pervVoucher()
      }
    },
    onNextVoucher() {
      if (this.$refs.summary) {
        this.$refs.summary.nextVoucher()
      }
    },
    async onCloseCustomStyleDialog() {
      await this.loadUserStyle()
      this.tableData = this.tableData.map(row => {
        const newRow = Object.assign({}, row)
        const keys = Object.keys(this.detailStyles)
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i]
          this.$set(row, key, this.detailStyles[key])
          console.log(key, this.detailStyles[key])
          // row[key] = this.detailStyles[key]
          newRow[key] = this.detailStyles[key]
        }
        return newRow
      })
      // this.$forceUpdate()
      // this.$refs['DTable'].resetDescState()
    },
    /* ---------------- 组件通信 ---------------- */
    handleReloadAllEnquiryPage() {
      this.$bus.emit('reloadAllEnquiryPage')
    },
    changeCommand(command) {
      this.form.vc_status = command
      // console.log(this.form);
    },
    async onExport() {
      try {
        const fy_code = this.fy_code
        const vc_no = this.form.vc_no || undefined
        const res = await exportVoucher({ fy_code, vc_no })
        console.log(res)
        const result = await exportExcel(res)
        console.log(result)
      } catch (e) {
        console.error(e)
      }
    },
    onImport() {
      this.importVisible = true
    },
    verifyHeader(headers) {
      const cols = [
        '*tx_num',
        '*ac_code',
        'IE',
        '*Descr',
        '*Amount_Dr',
        '*Amount_Cr',
        'Payee',
        'Ref',
        'Staff',
        'Budget',
        'Contra',
        'Q_Type',
        'Q_Num',
      ]
      return cols.every(i => {
        return headers.findIndex(h => h === i) !== -1
      })
    },
    verifyData(data) {
      const cols = ['*tx_num', '*ac_code', '*Descr', '*Amount_Dr', '*Amount_Cr']
      const err = []
      for (let i = 0; i < data.length; i++) {
        const row = data[i]
        for (let j = 0; j < cols.length; j++) {
          const key = cols[j]
          if (!row.hasOwnProperty(key)) {
            err.push(this.$t('daily.voucher.message.importRowErr', { row: i + 2, field: key }))
          }
        }
      }
      return err
    },
    handleImport({ header, results }) {
      console.log({ header, results })
      // if (!this.verifyHeader(header)) {
      //   this.$message.error('導入模板標題錯誤')
      //   return
      // }
      // const dataErr = this.verifyData(results)
      // if (dataErr.length > 0) {
      //   const h = this.$createElement;
      //   this.$message.error({ message: h('div', null, dataErr.map(i => h('p', null, i))) })
      //   return
      // }
      // for (let i = 0; i < results.length; i++) {
      //   const row = results[i]
      //
      //   // const cols = ["*tx_num", "*ac_code", "IE", "*Descr", "*Amount_Dr", "*Amount_Cr", "Payee",
      //   //   "Ref", "Staff", "Budget", "Contra", "Q_Type", "Q_Num"]
      //   const tx_num = row['*tx_num']
      //   const ac_code = row['*ac_code']
      //   const amount_cr = row['*Amount_Cr']
      //   const amount_dr = row['*Amount_Dr']
      //   const descr = row['*Descr']
      //   const budget_code = row['Budget']
      //   const ref = row['Ref']
      //   const st_code = row['Staff']
      //   const tx_type = row['IE']
      //   const vc_contra = row['Contra']
      //   const vc_payee = row['Payee']
      //   const vc_quote = row['Q_Type']
      //   const vc_qnum = row['Q_Num']
      //   this.tableData.push({
      //     ac_code,
      //     ac_name_cn: "",
      //     ac_name_en: "",
      //     account_id: 0,
      //     amount_cr,
      //     amount_dr,
      //     budget_code,
      //     budget_name_cn: "",
      //     budget_name_en: "",
      //     contra_name_cn: null,
      //     contra_name_en: null,
      //     dept_name_cn: "",
      //     dept_name_en: "",
      //     descr,
      //     lg_id: 0,
      //     lg_type: "",
      //     ref,
      //     st_code,
      //     st_name_cn: "",
      //     st_name_en: "",
      //     tx_num,
      //     tx_type,
      //     vc_contra,
      //     vc_date: "",
      //     vc_dept: "",
      //     vc_method: "",
      //     vc_no: "",
      //     vc_order: null,
      //     vc_payee,
      //     vc_pcode: null,
      //     vc_qnum,
      //     vc_quote,
      //     vc_rdate: null,
      //     vc_rstatus: "",
      //     vc_status: 0,
      //   })
      // }

      const fy_code = this.fy_code
      importExcel(this, importVoucher, results, header, { fy_code })
        .then(data => {
          console.log('importVoucher', data)
          const arr = []
          for (let i = 0; i < data.length; i++) {
            const row = data[i]
            if (row.tx_num === 0) {
              console.log(this.$refs.summary)
              const list = (this.$refs.summary && this.$refs.summary.voucherTypeList) || []
              const voucherType = list.find(i => i.vt_ac_code === row.ac_code)
              if (voucherType) {
                this.form.vt_ac_code = row.ac_code
                this.form.vt_code = voucherType.vt_code
                this.form.vt_category = voucherType.vt_category
                this.form.vt_description_cn = voucherType.vt_description_cn
                this.form.vt_description_en = voucherType.vt_description_en
              }
              this.form.account_id = row.account_id
              this.form.ac_name_cn = row.ac_name_cn
              this.form.ac_name_en = row.ac_name_en
              this.form.vc_summary = row.descr
              // this.form.amount_cr = row.amount_cr
              // this.form.amount_dr = row.amount_dr
              this.form.vc_payee = row.vc_payee
              this.form.ref = row.ref
              if (row.ref && this.form.vc_method !== 'OTHER' && this.form.vc_method !== 'CHEQUE') {
                this.form.vc_method = 'CHEQUE'
              }
              this.form.vc_st_code = row.st_code
              this.form.st_name = row[this.isEnglish ? 'st_name_en' : 'st_name_cn']
              this.form.st_name_en = row.st_name_en
              this.form.st_name_cn = row.st_name_cn
            } else {
              const item = {
                ac_code: '',
                ac_name_cn: '',
                ac_name_en: '',
                account_id: 0,
                amount_cr: '',
                amount_dr: '',
                budget_code: '',
                budget_name_cn: '',
                budget_name_en: '',
                contra_name_cn: null,
                contra_name_en: null,
                dept_name_cn: '',
                dept_name_en: '',
                descr: '',
                lg_id: 0,
                lg_type: '',
                ref: '',
                st_code: '',
                st_name_cn: '',
                st_name_en: '',
                tx_num: '',
                tx_type: '',
                vc_contra: '',
                vc_date: '',
                vc_dept: '',
                vc_method: '',
                vc_no: '',
                vc_order: null,
                vc_payee: '',
                vc_pcode: null,
                vc_qnum: '',
                vc_quote: '',
                vc_rdate: null,
                vc_rstatus: '',
                vc_status: 0,
              }
              // 會計科目
              item.ac_code = row.ac_code
              item.account_id = row.account_id
              item.ac_name_cn = row.ac_name_cn
              item.ac_name_en = row.ac_name_en
              // 會計科目-類
              if (row.ac_E === 'Y') {
                item.tx_type = 'E'
                item.tx_type_disable = false
              } else if (data.ac_I === 'Y') {
                item.tx_type = 'I'
                item.tx_type_disable = false
              } else {
                item.tx_type = ''
                item.tx_type_disable = true
              }
              item.descr = row.descr
              item.amount_cr = row.amount_cr
              item.amount_dr = row.amount_dr
              item.vc_payee = row.vc_payee
              item.ref = row.ref
              item.ac_B = row.ac_B
              item.ac_E = row.ac_E
              item.ac_I = row.ac_I
              // 職員
              item.st_code = row.st_code
              item.st_name_cn = row.st_name_cn
              item.st_name_en = row.st_name_en
              item.st_name = row[this.isEnglish ? 'st_name_en' : 'st_name_cn']
              item.staff_is_error = false
              // 部門
              item.vc_dept = row.vc_dept
              item.dept_name_en = row.dept_name_en
              item.dept_name_cn = row.dept_name_cn
              item.dept_name = row[this.isEnglish ? 'dept_name_en' : 'dept_name_cn']
              item.dept_is_error = false
              // 預算
              item.budget_code = row.budget_code
              item.budget_name_cn = row.budget_name_cn
              item.budget_name_en = row.budget_name_en
              // 對沖
              item.vc_contra = row.vc_contra
              item.contra_name_en = row.contra_name_en
              item.contra_name_cn = row.contra_name_cn
              item.contra_name = row[this.isEnglish ? 'contra_name_en' : 'contra_name_cn']
              item.contra_is_error = false
              // 報賬
              item.vc_qnum = row.vc_qnum
              item.vc_quote = row.vc_quote

              arr.push(item)
            }
          }
          this.tableData = arr
          this.$forceUpdate()
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importVisible = false
        })

      // this.$message.success('導入成功')
    },
  },
}
</script>

<style lang="scss" scoped>
i {
  font-size: 20px;
  vertical-align: middle;
  line-height: 25px;
}
.payment-info {
  /*margin: 5px 0;*/
  margin: 0;
  height: 100%;
  overflow-y: auto;
  /*/deep/ .el-input--medium .el-input__icon, /deep/ .el-input__prefix{*/
  /deep/ {
    .el-form-item--medium .el-form-item__content,
    .el-form-item--medium .el-form-item__label,
    .el-input--medium .el-input__inner,
    .el-input--medium .el-input__icon {
      line-height: 25px !important;
      height: 25px !important;
    }
    /*.el-form-item__content{*/
    /*  width: calc(100% - 80px);*/
    /*}*/
    .actions {
      .el-input {
        line-height: 25px;
        height: 25px;
        max-width: 150px;
      }
    }

    .filter,
    .actions {
      min-width: 555px;
    }

    /*[class^='el-input'],*/
    [class^='el-form'],
    .el-input__icon {
      vertical-align: middle;
    }
    .el-form-item__label {
      vertical-align: sub;
      padding: 0 5px 0 0;
    }
    .el-button--mini {
      padding: 5px 10px;
    }
    .el-button + .el-button {
      margin-left: 5px;
    }
    .el-radio + .el-radio {
      margin-left: 0px;
    }
    .el-input--suffix .el-input__inner {
      /*padding-right: 3px;*/
      padding-right: 23px;
    }
    .el-radio__label {
      padding-left: 2px;
    }
    .el-form--inline .el-form-item__content {
      vertical-align: middle;
    }
  }
  .info-form {
    padding: 5px;
  }
  .actions {
    /*background: #CDEFFF;*/
    padding: 2px 0;
  }
  .info {
    background: #f4f4f4;
  }
  .edac-icon {
    color: #68afff;
    &.edac-icon-import,
    &.edac-icon-export {
      float: right;
      background: #68afff;
      color: #ffffff;
      height: 20px;
      width: 20px;
      text-align: center;
      vertical-align: middle;
      line-height: 20px;
      margin-top: 2px;
      margin-right: 2px;
      border-radius: 2px;
    }
  }
}
</style>
