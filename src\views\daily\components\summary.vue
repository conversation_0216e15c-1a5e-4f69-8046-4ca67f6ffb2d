<template>
  <!--  傳票信息  -->
  <div :class="'summary' + (isView ? ' is-view' : '')">
    <el-form
      ref="info-form"
      :inline="true"
      :model="info"
      :disabled="isView"
      :validate-on-rule-change="true"
      class="info-form"
    >
      <!--第一行-->
      <el-row>
        <!-- 類型 -->
        <el-form-item
          :rules="rules"
          :label="$t('daily.label.type')"
          prop="vt_code"
          label-width="80px"
        >
          <el-select v-model="info.vt_code" style="width: 300px" @change="changeVoucherType">
            <!-- :disabled="action !== 'add'" -->
            <el-option
              v-for="item in voucherTypeList"
              :key="item.voucher_type_id"
              :label="getVoucherTypeLabel(item)"
              :value="item.vt_code"
            />
          </el-select>
        </el-form-item>

        <!-- 借方總額 -->
        <el-form-item
          v-if="!amt_type"
          :label="$t('daily.label.drAmtSum')"
          :rules="[
            {
              validator: amountValidator,
              trigger: 'blur',
            },
          ]"
          prop="vc_amount"
          label-width="80px"
          style="margin-right: 0; float: right"
        >
          <ENumeric
            :style="isDiffDrAmount ? 'color: red;' : ''"
            :value="dr_sum"
            :read-only="true"
            class="aaa"
            style="
              -webkit-appearance: none;
              background-color: #f4f4f4;
              background-image: none;
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              -webkit-box-sizing: border-box;
              box-sizing: border-box;
              color: #606266;
              display: inline-block;
              font-size: inherit;
              outline: 0;
              -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              width: 120px;
              text-align: right;
              padding: 0px 2px;
            "
          />
        </el-form-item>

        <!--    類別    -->
        <el-form-item
          v-if="'PCR'.includes(info.vt_category)"
          prop="vc_receipt"
          style="float: right; display: inline-block"
        >
          <el-date-picker
            v-if="vc_rdate"
            v-model="vc_rdate"
            readonly
            :format="styles.dateFormat"
            value-format="yyyy-MM-dd"
            type="date"
            style="display: inline-block; width: 110px; margin-right: 10px"
          />
          <i
            :class="'icon-receipt' + (info.vc_receipt === 'X' ? ' no-selected' : '')"
            @click="handleTriggerReceipt"
          >
            <svg-icon icon-class="paid" />
          </i>
          <el-input
            v-if="info.vt_category === 'R' && info.vc_receipt !== 'X'"
            v-model="info.vc_receipt"
            style="display: inline-block; width: 80px"
          />
          <el-checkbox
            v-else-if="info.vt_category !== 'R' || info.vc_receipt !== 'X'"
            v-model="hasReceipt"
            :disabled="info.vc_receipt === 'X'"
            @change="ChangeReceipt"
          />
        </el-form-item>
      </el-row>
      <!--第二行-->
      <el-row>
        <!--    傳票日期    -->
        <el-form-item
          :rules="rules"
          :label="$t('daily.label.vc_date')"
          prop="vc_date"
          label-width="80px"
        >
          <el-date-picker
            v-model="info.vc_date"
            :clearable="false"
            :placeholder="$t('placeholder.selectDate')"
            :format="styles.dateFormat"
            value-format="yyyy-MM-dd"
            style="width: 195px"
            type="date"
            @change="changeDate"
          />
        </el-form-item>

        <div style="display: inline-block">
          <!--    圖案按鈕    -->
          <el-form-item
            v-if="amt_type"
            :label="payeeLabel"
            :class="[
              {
                'is-new-value': data._payeeIsNew,
              },
            ]"
            prop="vc_payee"
            label-width="102px"
            style="margin-right: 0"
          >
            <!--

            :rules="[
              {
                validator: validatePayee,
                trigger: ['change', 'blur']
              }
            ]"
            -->
            <!--<el-input v-model="info.vc_payee" style="width: 300px"/>-->
            <el-autocomplete
              v-model="info.vc_payee"
              :fetch-suggestions="searchPayee"
              placeholder=""
              style="width: 300px"
              @select="handleSelectPayee"
            />
            <i
              v-if="amt_type"
              class="edac-icon action-icon edac-icon-search"
              @click="onSelectPayee"
            />
          </el-form-item>
        </div>
        <!-- 貸方總額 -->
        <el-form-item
          v-if="!amt_type"
          :label="$t('daily.label.crAmtSum')"
          :rules="[
            {
              validator: amountValidator,
              trigger: 'blur',
            },
          ]"
          prop="vc_amount"
          label-width="80px"
          style="margin-right: 0; float: right"
        >
          <ENumeric
            :style="isDiffCrAmount ? 'color: red;' : ''"
            :value="cr_sum"
            :read-only="true"
            class="aaa"
            style="
              -webkit-appearance: none;
              background-color: #f4f4f4;
              background-image: none;
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              -webkit-box-sizing: border-box;
              box-sizing: border-box;
              color: #606266;
              display: inline-block;
              font-size: inherit;
              outline: 0;
              padding: 0 2px;
              -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              width: 120px;
              text-align: right;
            "
          />
        </el-form-item>
        <el-select
          v-if="dropdownShow && !amt_type && info.vt_category == 'J'"
          v-model="dropdowntitle"
          :placeholder="$t('daily.label.normal')"
          class="downShow"
          @change="handleCommand"
        >
          <el-option
            v-for="item in correctList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-form-item :rules="rules" prop="vc_extra">
          <i class="edac-icon action-icon edac-icon-buyer" @click="handleTriggerPayee" />
          <el-checkbox v-model="info.vc_extra" true-label="Y" false-label="N" />
        </el-form-item>
      </el-row>

      <!--第三行-->
      <el-row>
        <!--    職員    -->
        <el-form-item
          ref="info-form-staff"
          :label="$t('daily.label.staff')"
          :rules="[
            {
              validator: validateStaff,
              trigger: ['blur'],
            },
          ]"
          :class="[
            {
              'is-invalid': staffInvalid,
            },
          ]"
          prop="st_name"
          label-width="80px"
        >
          <!--vc_st_code -->
          <!--          <el-select/>-->
          <!--          <el-input v-model="info.st_name" style="width: 195px"/>-->
          <!--   注釋    -->
          <el-select
            v-model="info.st_name"
            :multiple="false"
            :filterable="true"
            :remote="true"
            :remote-method="staffRemoteMethod"
            :loading="staffLoading"
            :allow-create="false"
            :default-first-option="false"
            :clearable="true"
            reserve-keyword
            placeholder=""
            style="width: 195px"
            @clear="onClearStaff"
          >
            <el-option
              v-for="item in selectStaffList"
              :key="item.staff_id"
              :label="item[isEnglish ? 'st_name_en' : 'st_name_cn']"
              :value="item[isEnglish ? 'st_name_en' : 'st_name_cn']"
            />
          </el-select>
          <i class="edac-icon action-icon edac-icon-search" @click="onSelectStaff" />
        </el-form-item>

        <div style="display: inline-block">
          <!--    內容描述    -->
          <el-form-item
            ref="info-form-desc"
            :rules="[
              {
                required: true,
                trigger: 'blur',
                message: ' ',
              },
            ]"
            :class="[
              {
                'is-new-value': data._descIsNew,
              },
            ]"
            :label="$t('daily.label.desc')"
            prop="vc_summary"
            label-width="77px"
            style="margin-right: 0"
          >
            <!--
              {
                validator: validateDesc,
                trigger: ['blur']
              }
              -->
            <!--<el-input v-model="info.vc_summary" style="width: 300px"/>-->
            <el-autocomplete
              v-model="info.vc_summary"
              :fetch-suggestions="searchDesc"
              placeholder=""
              style="width: 300px"
              @select="handleSelectDesc"
              @blur="handleBlurDesc"
            />
            <i class="edac-icon action-icon edac-icon-search" @click="onSelectDesc()" />
            <i class="edac-icon action-icon edac-icon-copy_down" @click="setChildrenDesc" />
            <i class="edac-icon action-icon edac-icon-copy_up" @click="loadTableDesc" />
            <!--            <i class="edac-icon action-icon edac-icon-accessory"/>-->
          </el-form-item>
        </div>
      </el-row>
      <!--第四行-->
      <el-row>
        <el-form-item
          v-if="amt_type"
          :rules="rules"
          :label="$t('daily.label.amt_type')"
          prop="vc_method"
          label-width="80px"
        >
          <el-radio-group v-model="info.vc_method" style="vertical-align: bottom">
            <el-radio :label="$t('voucher_method.value.transf')">
              {{ $t('voucher_method.transf') }}
            </el-radio>
            <el-radio :label="$t('voucher_method.value.cash')">
              {{ $t('voucher_method.cash') }}
            </el-radio>
            <el-radio :label="$t('voucher_method.value.cheque')" @click.native="onSelectedCheque">
              {{ $t('voucher_method.cheque') }}
            </el-radio>
            <el-radio :label="$t('voucher_method.value.auto')">
              {{ $t('voucher_method.auto') }}
            </el-radio>
            <el-radio :label="$t('voucher_method.value.other')">
              {{ $t('voucher_method.other') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="amt_type && info.vc_method === 'CHEQUE'"
          :label="$t('daily.label.number')"
          prop="ref"
          label-width="40px"
          style="margin-right: 0"
        >
          <el-input v-model="info.ref" style="width: 80px" />
        </el-form-item>
        <el-form-item
          v-else-if="amt_type && info.vc_method === 'OTHER'"
          label=""
          label-width="40px"
          style="margin-right: 0"
        >
          <el-input v-model="info.ref" style="width: 120px" />
        </el-form-item>
        <el-form-item
          v-if="amt_type && info.vc_method === 'CHEQUE'"
          :label="$t('daily.label.date')"
          label-width="40px"
          prop="vc_chq_date"
          style="margin-right: 0"
        >
          <el-date-picker
            v-model="info.vc_chq_date"
            :placeholder="$t('placeholder.selectDate')"
            :format="styles.dateFormat"
            value-format="yyyy-MM-dd"
            type="date"
            style="width: 130px"
            class="summary-date-picker"
          />
        </el-form-item>
        <el-form-item
          v-if="amt_type"
          :label="$t('daily.label.amtSum')"
          label-width="40px"
          style="margin-right: 0; float: right"
        >
          <ENumeric
            :style="isDiffAmount ? 'color: red;' : ''"
            :value="total"
            :read-only="true"
            class="aaa"
            style="
              -webkit-appearance: none;
              background-color: #f4f4f4;
              background-image: none;
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              -webkit-box-sizing: border-box;
              box-sizing: border-box;
              color: #606266;
              display: inline-block;
              font-size: inherit;
              outline: 0;
              padding: 2px 4px;
              -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              width: 120px;
              text-align: right;
            "
          />
        </el-form-item>
      </el-row>
    </el-form>

    <!--選擇收款人 彈窗-->
    <dialogPayee
      v-if="info.fy_code"
      :dialog-visible.sync="dialogPayeeVisible"
      :fy_code="info.fy_code"
      @selectRow="handlePayeeSelect"
    />
    <!--選擇描述 彈窗-->
    <dialogDescription
      v-if="info.fy_code"
      :dialog-visible.sync="dialogDescVisible"
      :fy_code="info.fy_code"
      :fund-id="fundId"
      @selectRow="handleDescSelect"
    />
    <!--選擇職員 彈窗-->
    <dialogStaff
      v-if="info.fy_code"
      :dialog-visible.sync="dialogStaffVisible"
      :fy_code="info.fy_code"
      @selectRow="handleStaffSelect"
    />
    <!-- 選擇傳票編號 -->
    <el-dialog :visible.sync="t_dialogNoListVisible" :title="$t('daily.dialog.voucherNO')">
      <el-row :gutter="10">
        <el-col
          v-for="item in noList"
          :key="item"
          :xs="8"
          :sm="6"
          :md="4"
          :lg="3"
          :xl="3"
          class="voucher-box"
        >
          <div
            :class="'voucher-no' + (info.vc_no === item ? ' selected' : '')"
            @click="selectVCNo(item)"
          >
            {{ item }}
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import ENumeric from '@/components/ENumeric'

import dialogPayee from '@/views/daily/components/DialogPayee'
import dialogDescription from '@/views/daily/components/DialogDescription'
import dialogStaff from '@/views/daily/components/DialogStaff'

// API
import { fetchVoucherTypes, fetchNoList } from '@/api/master/voucherType'
import { searchCompanies } from '@/api/assistance/payeePayer'
import { searchStaffs } from '@/api/assistance/staff'
import { fetchDescriptions } from '@/api/assistance/description'

import { deepCloneByJSON, toDecimal } from '@/utils'

export default {
  name: 'Summary',
  components: {
    VBreadCrumb,
    ENumeric,
    dialogPayee,
    dialogDescription,
    dialogStaff,
  },
  props: {
    isView: {
      type: Boolean,
      default: false,
    },
    dialogNoListVisible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    action: {
      type: String,
      default: 'add',
    },
    tableAmount: {
      type: Number,
      default: 0,
    },
    oldAmount: {
      type: Number,
      default: 0,
    },
    oldDrAmount: {
      type: Number,
      default: 0,
    },
    oldCrAmount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      hasReceipt: false,
      info: this.formatData(this.data),
      voucherTypeList: [],

      dialogPayeeVisible: false,
      dialogDescVisible: false,
      dialogStaffVisible: false,

      dr_sum: 0,
      cr_sum: 0,

      receipt_text: this.data.vc_receipt,

      noList: [],
      t_dialogNoListVisible: this.dialogNoListVisible,

      // payeeIsNew: false,
      // descIsNew: false,
      staffInvalid: false,
      currentVoucherType: '',

      dropdownShow: false,
      dropdowntitle: this.$t('daily.label.normal'),

      correctList: [
        {
          value: 1,
          label: this.$t('daily.label.normal'),
        },
        {
          value: 2,
          label: this.$t('daily.label.correctOne'),
        },
        {
          value: 3,
          label: this.$t('daily.label.correctTwo'),
        },
        {
          value: 4,
          label: this.$t('daily.label.correctThree'),
        },
      ],
      selectStaffList: [],
      staffLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    amt_type() {
      return 'PCR'.includes(this.info.vt_category)
    },
    payeeLabel() {
      switch (this.info.vt_category) {
        case 'P':
        case 'C':
          return this.$t('daily.label.payee_receipt')
        case 'R':
          return this.$t('daily.label.payee_payment')
        default:
          return this.$t('daily.label.payee')
      }
    },
    vc_rdate() {
      if (this.data && this.data.vc_rdate) {
        return this.data.vc_rdate
      }
      return ''
    },
    fundId() {
      if (this.voucherTypeList.length && this.info.vt_code) {
        const fund = this.voucherTypeList.find(i => i.vt_code === this.info.vt_code)
        if (fund) {
          return fund.fund_id
        }
      }
      return ''
    },
    total() {
      let sum = 0
      for (let i = 0; i < this.tableData.length; i++) {
        const item = this.tableData[i]
        const vt_category = (this.info && this.info.vt_category) || ''
        let v = 0
        if (vt_category === 'P' || vt_category === 'C') {
          v = toDecimal(item.amount_dr)
        } else if (vt_category === 'R') {
          v = toDecimal(item.amount_cr)
        } else {
          if (item.select_amount_type === 'Dr') {
            v = toDecimal(item.amount_dr)
          } else {
            v = toDecimal(item.amount_cr)
          }
        }
        sum += v
      }
      return sum
    },
    isDiffAmount() {
      console.log('this.form', this.data)
      return this.total !== this.oldAmount && this.data.vc_rdate
    },
    isDiffDrAmount() {
      console.log('this.form', this.data)
      return this.dr_sum !== this.oldDrAmount && this.data.vc_rdate
    },
    isDiffCrAmount() {
      console.log('this.form', this.cr_sum, this.oldCrAmount)
      return this.cr_sum !== this.oldCrAmount && this.data.vc_rdate
    },
  },
  watch: {
    info: {
      deep: true,
      immediate: false,
      handler(value) {
        if (value) {
          this.currentVoucherType = value.vt_code
        }
        this.$emit('update:data', value)
      },
    },
    tableData: {
      deep: true,
      immediate: true,
      handler(value) {
        this.tableSum()
      },
    },
    dialogNoListVisible: {
      immediate: true,
      handler(value) {
        this.t_dialogNoListVisible = value
      },
    },
    t_dialogNoListVisible: {
      immediate: true,
      handler(value) {
        this.$emit('update:dialogNoListVisible', value)
      },
    },
    'info.vc_date'(newVal, oldVal) {
      if (!newVal) return
      if (newVal === oldVal) return
      const o = new Date(oldVal)
      const os = o.getFullYear() + '-' + o.getMonth()
      const n = new Date(newVal)
      const ns = n.getFullYear() + '-' + n.getMonth()
      if (os === ns) return

      this.loadNoList(true)
    },
  },
  created() {
    this.initData()
  },
  mounted() {
    this.hasReceipt = this.info.vc_receipt === 'Y'
    this.changeDate(this.info.vc_date)
  },
  methods: {
    ...mapActions(['getCycleDate']),
    isNewFormat(newVal, oldVal) {
      if (this.voucherTypeList.length) {
        const n = this.voucherTypeList.find(i => i.vt_code === newVal)
        const o = this.voucherTypeList.find(i => i.vt_code === oldVal)
        if (!o || !n) {
          this.currentVoucherType = ''
          return true
        }
        if (o.vt_format === n.vt_format) {
          return false
        }
      }
      return true
    },
    getVoucherTypeLabel(item) {
      const { vt_code, vt_ac_code } = item
      const name = this.language === 'en' ? item.vt_description_en : item.vt_description_cn
      let label = `[${vt_code}]`
      if (vt_ac_code) {
        label += `[${vt_ac_code}]`
      }
      return label + ' ' + name
    },
    formatData(data) {
      const newData = deepCloneByJSON(data)
      if (!newData.hasOwnProperty('st_name_cn')) {
        newData.st_name_cn = ''
        newData.st_name_en = ''
      }
      newData.st_name = this.language === 'en' ? newData.st_name_en : newData.st_name_cn
      this.hasReceipt = newData.vc_receipt === 'Y'
      return newData
    },
    tableSum() {
      let dr = 0
      let cr = 0
      this.tableData.forEach(item => {
        dr = toDecimal(dr + parseFloat(item.amount_dr))
        cr = toDecimal(cr + parseFloat(item.amount_cr))
      })
      if (this.tableData[0].vc_status === 1) {
        this.dropdowntitle = this.$t('daily.label.normal')
      } else if (this.tableData[0].vc_status === 2) {
        this.dropdowntitle = this.$t('daily.label.correctOne')
      } else if (this.tableData[0].vc_status === 3) {
        this.dropdowntitle = this.$t('daily.label.correctTwo')
      } else if (this.tableData[0].vc_status === 4) {
        this.dropdowntitle = this.$t('daily.label.correctThree')
      }
      this.dr_sum = dr
      this.cr_sum = cr
      return dr === 0 ? cr : dr
    },
    initData(forceUpdate) {
      return new Promise((resolve, reject) => {
        fetchVoucherTypes({ vt_category: this.info.vt_category, fy_code: this.info.fy_code })
          .then(res => {
            this.voucherTypeList = res
            // 設置默認類別
            if (res.length === 0) {
              // resolve()
              return Promise.reject()
            }

            let item
            if (this.info.vt_code) {
              item = res.find(i => i.vt_code === this.info.vt_code)
            }
            if (!item) {
              item = res[0]
            }
            this.$set(this.info, 'vt_code', item.vt_code)
            this.$set(this.info, 'fund_id', item.fund_id)
            this.$set(this.info, 'voucher_type_id', item.voucher_type_id)
            return Promise.resolve()
          })
          .then(() => {
            // 查看是否獲取fy_code
            if (!this.info.vc_date) {
              return Promise.reject(this.$t('daily.voucher.message.selectVoucherDate'))
            }
            return Promise.resolve()
          })
          .then(() => {
            this.loadNoList(forceUpdate)
          })
      })
    },
    loadNoList(forceUpdate) {
      return new Promise((resolve, reject) => {
        fetchNoList({
          voucher_type_id: this.info.voucher_type_id,
          date: this.info.vc_date,
          vc_no: this.info.vc_no ? this.info.vc_no : undefined,
        })
          .then(res => {
            this.noList = res
            console.log(forceUpdate, this.info.vc_no, res.length, 'res')
            console.log(res[0], 'res[0]')
            if ((forceUpdate || !this.info.vc_no) && res.length) {
              this.$set(this.info, 'vc_no', res[0])
            }
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },

    // dom
    handleTriggerReceipt() {
      if (this.isView) return
      if (this.info.vc_receipt === 'X') {
        if (this.hasReceipt) {
          this.info.vc_receipt = 'Y'
        } else {
          if (this.info.vt_category === 'R') {
            this.hasReceipt = true
            this.info.vc_receipt = ''
          } else {
            this.info.vc_receipt = ''
          }
        }
      } else {
        this.info.vc_receipt = 'X'
        this.hasReceipt = false
      }
    },
    handleTriggerPayee() {
      if (this.isView) return
    },
    ChangeReceipt(val) {
      if (this.isView) return
      if (this.info.vc_receipt !== 'X') {
        this.info.vc_receipt = val ? 'Y' : ''
      }
    },
    handlePayeeSelect(data) {
      if (this.isView) return
      if (data && data.comp_name) {
        this.info.vc_payee = data.comp_name
      }
    },
    //
    onSelectPayee() {
      if (this.isView) return
      this.dialogPayeeVisible = true
    },
    //
    onSelectStaff() {
      if (this.isView) return
      this.dialogStaffVisible = true
    },
    // 描述
    onSelectDesc() {
      if (this.isView) return
      this.dialogDescVisible = true
    },
    handleDescSelect(data) {
      if (this.isView) return
      if (data) {
        this.$set(this.info, 'vc_summary', data.desc)
        // this.descIsNew = false
        this.$set(this.info, '_descIsNew', false)
        this.$refs['info-form-desc'].clearValidate()
        this.handleBlurDesc()
      }
    },
    onClearStaff() {
      this.$set(this.info, 'vc_st_code', '')
      this.$set(this.info, 'st_name_en', '')
      this.$set(this.info, 'st_name_cn', '')
      this.$set(this.info, 'st_name', '')
    },
    handleStaffSelect(data) {
      if (this.isView) return
      if (data) {
        this.$set(this.info, 'vc_st_code', data.st_code)
        this.$set(this.info, 'st_name_en', data.st_name_en)
        this.$set(this.info, 'st_name_cn', data.st_name_cn)
        this.$set(this.info, 'st_name', this.language === 'en' ? data.st_name_en : data.st_name_cn)
        this.handleReloadStaffEnquiryPage(data.st_code)
        this.staffInvalid = false
        this.$refs['info-form-staff'].clearValidate()
      }
    },
    loadTableDesc() {
      if (this.isView) return
      const arr = []
      this.tableData.forEach(item => {
        if (arr.indexOf(item.descr) === -1) {
          arr.push(item.descr)
        }
      })
      this.$set(this.info, 'vc_summary', arr.filter(i => i).join(' / '))
      this.$nextTick(() => {
        this.handleBlurDesc()
      })
    },
    setChildrenDesc() {
      if (this.isView) return
      const newData = this.tableData.map(item => {
        item.descr = this.info.vc_summary
        return item
      })
      this.$emit('update:tableData', newData)
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs['info-form'].validate((valid, data) => {
          if (valid) {
            if (
              (this.info.vt_category === 'T' || this.info.vt_category === 'J') &&
              this.dr_sum !== this.cr_sum
            ) {
              reject([])
            } else {
              resolve(data)
            }
          } else {
            reject(data)
          }
        })
      })
    },
    changeDate(date) {
      this.getCycleDate(date).then(res => {
        if (res.fy_code) {
          this.info.fy_code = res.fy_code
          this.$set(this.info, 'fy_code', res.fy_code)
          this.initData(true)
        }
        if (date === res.the_last_day) {
          this.dropdownShow = true
        } else {
          this.dropdownShow = false
        }
      })
    },
    handleCommand(command) {
      this.$emit('changeCommand', command)
    },
    changeVoucherType(val) {
      if (!val) return
      const item = this.voucherTypeList.find(i => i.vt_code === val)
      if (!item) {
        console.log('error', '未選擇賬目類別')
        this.$message.error(this.$t('daily.voucher.message.selectFund'))
        return
      }
      this.$set(this.info, 'voucher_type_id', item.voucher_type_id)
      this.$set(this.info, 'fund_id', item.fund_id)

      const ac_code = item.vt_ac_code
      const fund_id = item.fund_id
      if (ac_code && fund_id) {
        this.$bus.emit('enquiryGrantFetch', fund_id, ac_code)
      }
      const v = this.voucherTypeList.find(i => i.vt_code === this.info.vt_code)
      const vt_code = v ? v.vt_code : ''
      const isNew = this.isNewFormat(this.currentVoucherType, vt_code)
      this.currentVoucherType = vt_code
      if (isNew) {
        this.loadNoList(true)
      }
    },
    selectVCNo(vc_no) {
      this.$set(this.info, 'vc_no', vc_no)
      this.t_dialogNoListVisible = false
    },
    /* 驗證 */
    amountValidator(rule, value, callback) {
      console.log(rule, value, callback)
      if (this.dr_sum !== this.cr_sum) {
        this.$message.error(this.$t('daily.voucher.message.dcNotEqual'))
        return callback(new Error(' '))
      }
      callback()
    },
    async validatePayee(rule, value, callback) {
      if (!value || (typeof value === 'string' && value.trim().length === 0)) {
        callback()
      } else {
        try {
          const res = await searchCompanies({ name: value })
          res.forEach(i => {
            i.value = i.comp_name
          })
          // this.payeeIsNew = !res.some(i => i.comp_name === value)
          this.$set(this.info, '_payeeIsNew', !res.some(i => i.comp_name === value))
        } catch (e) {
          callback()
        }
      }
    },
    async validateDesc(rule, value, callback) {
      if (value.trim().length === 0) {
        callback(new Error(' '))
      } else {
        try {
          const res = await fetchDescriptions({ desc: value })
          // if (res.length > 0) {
          //   this.descIsNew = false
          // } else {
          //   this.descIsNew = true
          // }
          // this.descIsNew = !res.some(i => i.desc === value)
          this.$set(this.info, '_descIsNew', !res.some(i => i.desc === value))
        } catch (e) {
          callback()
        }
      }
    },
    async validateStaff(rule, value, callback) {
      if (!value || (typeof value === 'string' && value.trim().length === 0)) {
        // this.$set(this.info, 'vc_st_code', '')
        // this.$set(this.info, 'st_name_en', '')
        // this.$set(this.info, 'st_name_cn', '')
        // this.$set(this.info, 'st_name', '')
        callback()
      } else {
        try {
          const fy_code = this.info.fy_code
          const res = await searchStaffs({ fy_code, name: value })
          if (res.length > 0) {
            this.staffInvalid = false
            const data = res[0]
            this.$set(this.info, 'vc_st_code', data.st_code)
            this.$set(this.info, 'st_name_en', data.st_name_en)
            this.$set(this.info, 'st_name_cn', data.st_name_cn)
            this.$set(
              this.info,
              'st_name',
              this.language === 'en' ? data.st_name_en : data.st_name_cn,
            )
            this.handleReloadStaffEnquiryPage(data.st_code)
          } else {
            this.staffInvalid = true
            // this.$set(this.info, 'vc_st_code', '')
            // this.$set(this.info, 'st_name_en', '')
            // this.$set(this.info, 'st_name_cn', '')
            // this.$set(this.info, 'st_name', '')
            callback(new Error(' '))
          }
        } catch (e) {
          callback()
        }
      }
    },
    onSelectedCheque() {
      this.info.vc_chq_date = this.info.vc_date
    },
    pervVoucher() {
      if (this.noList.length) {
        const currentNo = this.info.vc_no
        if (currentNo) {
          const i = this.noList.findIndex(i => i === currentNo)
          if (i === -1) {
            this.$set(this.info, 'vc_no', this.noList[0])
          } else if (i > 0) {
            this.$set(this.info, 'vc_no', this.noList[i - 1])
          }
        } else {
          this.$set(this.info, 'vc_no', this.noList[0])
        }
      }
    },
    nextVoucher() {
      if (this.noList.length) {
        const currentNo = this.info.vc_no
        if (currentNo) {
          const i = this.noList.findIndex(i => i === currentNo)
          if (i === -1) {
            this.$set(this.info, 'vc_no', this.noList[0])
          } else if (i >= 0 && i < this.noList.length - 1) {
            this.$set(this.info, 'vc_no', this.noList[i + 1])
          }
        } else {
          this.$set(this.info, 'vc_no', this.noList[0])
        }
      }
    },
    /* ---------------- 组件通信 ---------------- */
    handleReloadStaffEnquiryPage(staffCode) {
      if (!staffCode) return
      this.$bus.emit('enquiryStaffFetch', staffCode)
    },
    async staffRemoteMethod(query) {
      if (query.length === 0) return []
      try {
        const fy_code = this.info.fy_code
        this.selectStaffList = await searchStaffs({ fy_code, name: query })
      } catch (e) {
        console.error(e)
      }
    },
    async searchDesc(value, cb) {
      if (!value || value.replace(/ /g, '').length === 0) {
        cb([])
        return
      }
      try {
        const res = await fetchDescriptions({ desc: value })
        res.forEach(i => {
          i.value = i.desc
        })
        // this.descIsNew = !res.some(i => i.desc === value)
        this.$set(this.info, '_descIsNew', !res.some(i => i.desc === value))
        cb(res)
      } catch (e) {
        console.error(e)
      }
    },
    handleSelectDesc(item) {
      console.log(item)
      this.info.vc_summary = item.desc
      // this.descIsNew = false
      this.$set(this.info, '_descIsNew', false)
    },
    async searchPayee(value, cb) {
      if (!value || value.replace(/ /g, '').length === 0) {
        cb && cb([])
        return
      }
      try {
        const res = await searchCompanies({ name: value })
        res.forEach(i => {
          i.value = i.comp_name
        })
        // this.payeeIsNew = !res.some(i => i.comp_name === value)
        this.$set(this.info, '_payeeIsNew', !res.some(i => i.comp_name === value))
        cb && cb(res)
      } catch (e) {
        console.error(e)
      }
    },
    handleSelectPayee(item) {
      console.log(item)
      this.info.vc_payee = item.comp_name
      // this.payeeIsNew = false
      this.$set(this.info, '_payeeIsNew', false)
    },
    // 描述失去焦點 如果下面的傳票沒有這個描述 賦值
    handleBlurDesc() {
      const newData = this.tableData.map(item => {
        if (!item.descr || item.descr.trim().length === 0) {
          item.descr = this.info.vc_summary
        }
        return item
      })
      this.$emit('update:tableData', newData)
    },
  },
}
</script>

<style lang="scss" scoped>
i {
  font-size: 20px;
  vertical-align: middle;
  line-height: 25px;
  &.icon-receipt {
    fill: #68afff;
    /* width: 30px; */
    height: 25px;
    line-height: 20px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    .svg-icon {
      width: 25px;
      height: 25px;
    }

    &.no-selected {
      .svg-icon {
        fill: #b9b7b8;
      }
    }
  }
}
.summary {
  background: #f4f4f4;
  margin: 5px 0;
  min-width: 555px;
  /deep/ {
    .el-form-item--medium .el-form-item__content,
    .el-form-item--medium .el-form-item__label,
    .el-input--medium .el-input__inner,
    .el-input--medium .el-input__icon {
      line-height: 25px !important;
      height: 25px !important;
    }
    .el-input {
      line-height: 25px;
      height: 25px;
      /*max-width: 150px;*/
    }
    /*[class^='el-input'],*/
    [class^='el-form'],
    .el-input__icon {
      vertical-align: middle;
    }
    .el-form-item__label {
      vertical-align: sub;
      text-wrap: nowrap;
      padding: 0 5px 0 0;
    }
    .el-button--mini {
      padding: 5px 10px;
    }
    .el-button + .el-button {
      margin-left: 5px;
    }
    .el-radio + .el-radio {
      margin-left: 5px;
    }
    .el-input--suffix .el-input__inner {
      padding-right: 3px;
    }
    .el-radio__label {
      padding-left: 5px;
    }
    .el-form--inline .el-form-item__content {
      vertical-align: middle;
    }
    .el-checkbox {
      margin-right: 0;
    }
  }
  .info-form {
    padding: 5px;
    /deep/ {
      .el-input:not(.el-date-editor) {
        > input {
          padding: 0 5px;
        }
      }
    }
  }

  .edac-icon.action {
    cursor: pointer;
  }
  &.is-view {
    .edac-icon.action {
      cursor: not-allowed;
    }
  }
  .edac-icon {
    color: #68afff;
    &.no-selected {
      color: #b9b7b8;
    }
  }
  .sum-input {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    outline: 0;
    padding: 0 15px;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  .is-new-value {
    /deep/ {
      input {
        color: #169a55;
      }
    }
  }
  .is-invalid {
    /deep/ {
      input {
        color: red;
      }
    }
  }
}
.voucher-box {
  padding: 0 !important;
  .voucher-no {
    cursor: pointer;
    border: 1px solid #eeeeee;
    padding: 5px;
    &:hover {
      background-color: #a0c8ff;
    }
    &.selected {
      background-color: #a0c8ff;
    }
  }
}

.downShow {
  margin-left: 76px;
  width: 100px;
}
</style>
<style lang="scss">
.EDAC.font-size-15,
.EDAC.font-size-16,
.EDAC.font-size-17,
.EDAC.font-size-18 {
  .el-form-item__label {
    width: 90px !important;
  }
  .summary-date-picker {
    width: 150px !important;
  }
}
</style>
