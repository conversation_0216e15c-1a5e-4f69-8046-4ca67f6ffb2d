<template>
  <div class="app-container">
    <VBreadCrumb :show="true" class="breadcrumb" />

    <el-form
      ref="form"
      v-loading="loading"
      :model="preferences.filters"
      label-position="right"
      label-width="120px"
    >
      <!-- 報表 -->
      <el-form-item :rules="rules" :label="$t('report.report')" class="report" prop="report">
        <el-select v-model="preferences.filters.report">
          <el-option
            v-for="item in reportList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <!-- 學年 -->
      <el-form-item :rules="rules" :label="$t('report.year')" class="year" prop="year">
        <el-select v-model="preferences.filters.year" @change="onChangeYear">
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :value="item.fy_code"
            :label="item.fy_name"
          />
        </el-select>
      </el-form-item>
      <!-- 月份 -->
      <el-form-item :rules="rules" :label="$t('report.month')" class="month" prop="month">
        <el-select v-model="preferences.filters.month">
          <el-option
            v-for="item in months"
            :key="item.pd_id"
            :value="item.pd_code"
            :label="item.pd_name"
          />
        </el-select>
      </el-form-item>
      <!-- 格式（Format） -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.fundSummaryTypesRelation.sch_type')"
        class="sch_type"
        prop="sch_type"
      >
        <el-select v-model="preferences.filters.sch_type">
          <el-option :label="$t('assistance.fundSummaryTypesRelation.sch_type_pri')" value="PRI" />
          <el-option :label="$t('assistance.fundSummaryTypesRelation.sch_type_sec')" value="SEC" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onExport">
          {{ $t('button.export') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import { mapGetters } from 'vuex'
import loadPreferences from '@/views/mixins/loadPreferences'
import { fetchYears, getYear } from '@/api/master/years'
import { exportFinancialStatement } from '@/api/assistance/fundSummary'
import { exportExcel } from '@/utils/excel'
import { exportNorrayEDB } from '@/api/assistance/edb'
import { exportSwdReport } from '@/api/report/excel'
import { exportEDBReport } from '@/api/report/edb'

const { exportFinancialReport } = require('@/api/report')

export default {
  name: 'SettingUserInfo',
  components: {
    VBreadCrumb,
  },
  mixins: [loadPreferences],
  data() {
    return {
      langKey: 'report.excelReport.',
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      preferences: {
        filters: {
          report: '',
          year: '',
          month: '',
          sch_type: 'PRI',
        },
      },
      years: [],
      months: [],
      importDialog: false,
    }
  },
  computed: {
    ...mapGetters(['system', 'school']),
    reportList() {
      const arr = [
        { value: 'financialStatement', label: this.$t('report.financialStatement') },
        { value: 'edb', label: this.$t('report.edb') },
        { value: 'swd', label: this.$t('report.swd') },
      ]
      if (this.school.sch_pre === 'S') {
        arr.splice(2, 0, { value: 'edbSecondarySchool', label: this.$t('report.edbSecondarySchool') })
      }
      return arr
    },
  },
  created() {
    console.log('initData')
    this.initData()
    this.saveUserLastPage()
  },
  methods: {
    async initData() {
      this.loading = false
      try {
        this.years = await fetchYears()
        await this.loadUserPreference()
        if (this.preferences.filters.year) {
          this.onChangeYear(this.preferences.filters.year)
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    async onImport() {
      const valid = await this.$refs['form'].validate()
      if (!valid) return

      this.importDialog = true
      // this.loading = true
      // importExcel(this, importDepartments, results, header)
      //   .then(() => this.fetchTree())
      //   .catch(() => {})
      //   .finally(() => {
      //     this.loading = false
      //     this.importDialog = false
      //   })
    },
    async onExport() {
      const valid = await this.$refs['form'].validate()
      if (!valid) return
      try {
        let api
        const params = {}
        const fy_code = this.preferences.filters.year
        const pd_code = this.preferences.filters.month
        const sch_type = this.preferences.filters.sch_type
        switch (this.preferences.filters.report) {
          case 'financialStatement':
            params.fy_code = fy_code
            params.pd_code = pd_code
            api = exportFinancialStatement
            break
          case 'edb':
            if (this.school.sch_pre === 'S') {
              params.pd_code = pd_code
              api = exportFinancialReport
            } else {
              params.fy_code = fy_code
              params.pd_code = pd_code
              api = exportNorrayEDB
            }
            break
          case 'edbSecondarySchool':
            params.fy_code = fy_code
            params.pd_code = pd_code
            params.sch_type = sch_type
            api = exportEDBReport
            break
          case 'swd':
            params.fy_code = fy_code
            params.pd_code = pd_code
            api = exportSwdReport
            break
        }
        if (!api) {
          return
        }
        this.loading = true
        const res = await api(params)
        await exportExcel(res, '')
        this.$message.success(this.$t('file.exportSuccess'))
      } catch (e) {
        // this.$message.error(this.$t('file.exportError'))
        console.log(e)
      }
      this.loading = false
    },
    async onChangeYear(code) {
      this.loading = true
      const year = this.years.find(y => y.fy_code === code)
      if (year) {
        const res = await getYear(year.fy_id)
        console.log(res)
        if (res && res.periods && res.periods.length > 0) {
          this.months = res.periods
          const old = res.periods.find(p => p.pd_code === this.preferences.filters.month)
          if (!old) {
            this.preferences.filters.month = res.periods[0].pd_code
          }
        } else {
          this.months = []
          this.preferences.filters.month = ''
        }
      } else {
        this.preferences.filters.year = ''
      }
      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  header {
    margin: 0 20px 20px 0;
  }
}
.year {
  width: 400px;
}
</style>
