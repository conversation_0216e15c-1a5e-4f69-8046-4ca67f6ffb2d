<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 描述 -->
      <el-form-item :rules="rules" :label="$t('assistance.description.label.desc')" prop="desc">
        <el-input v-model="form.desc" clearable />
      </el-form-item>
      <!-- 編號 -->
      <el-form-item
        id="multi-ac-code"
        :rules="{ validator: validateACCode, trigger: 'blur' }"
        :label="$t('assistance.description.label.ac_code')"
        prop="multi_ac_code"
      >
        <el-button size="mini" type="primary" @click="dialogVisible = true">
          {{ $t('button.select') }}
        </el-button>
        <el-table :show-header="false" :data="selectAccountList" style="width: 100%">
          <el-table-column :label="$t('table.index')" type="index" />
          <el-table-column :label="$t('table.account')">
            <template v-if="scope && scope.row" slot-scope="scope">
              <span>{{
                `[${scope.row.code}]${scope.row[language === 'en' ? 'name_en' : 'name_cn']}`
              }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('table.action')" width="180">
            <template v-if="scope && scope.row" slot-scope="scope">
              <span>
                <i class="el-icon-delete" @click="removeAccount(scope.row)" />
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <!-- BIE -->
      <el-form-item :label="$t('assistance.description.label.BIE')">
        <el-checkbox-group v-model="BIE_arr">
          <el-checkbox v-for="item in BIE_list" :key="item.value" :label="item.label" />
        </el-checkbox-group>
      </el-form-item>

      <!-- 活躍年度 -->
      <el-form-item :label="$t('assistance.description.label.active_year')">
        <el-checkbox-group v-model="active_year_arr">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      :visible.sync="dialogVisible"
      :title="$t('assistance.description.label.selectAccount')"
      width="500px"
      append-to-body
    >
      <div>
        <el-form>
          <!-- 賬目類別 -->
          <el-form-item :rules="rules" :label="$t('assistance.description.label.fund_id')">
            <el-select v-model="fund_id" class="fund" @change="changeFund">
              <el-option
                v-for="item in funds"
                :key="item.fund_id"
                :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
                :value="item.fund_id"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <!--會計科目-->
        <el-tree
          :data="accounts"
          :expand-on-click-node="false"
          :show-checkbox="false"
          node-key="code"
          default-expand-all
        >
          <span slot-scope="{ data }" class="custom-tree-node">
            <span v-if="!data.fund_id">
              <el-checkbox
                v-model="data.checked"
                style="margin-right: 0"
                @change="setAccount($event, data)"
              />
            </span>
            <span>{{ data.fund_id ? '' : `[${data.code}]`
            }}{{ data[language === 'en' ? 'name_en' : 'name_cn'] }}</span>
          </span>
        </el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <!--        <el-button @click="dialogVisible = false">取 消</el-button>-->
        <el-button type="primary" @click="dialogVisible = false">{{
          $t('button.confirm')
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getDescription, createDescription } from '@/api/assistance/description'
import { fetchFunds } from '@/api/master/funds'
import { fetchYears } from '@/api/master/years'
import { getAccountTree } from '@/api/master/account'

export default {
  name: 'AssistanceDescriptionAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    defaultAccount: {
      type: String,
      default: '',
    },
    fyCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      form: {
        desc_id: '',
        multi_ac_code: '',
        desc: '',
        BIE: '',
        active_year: '',
      },
      defaultForm: {
        desc_id: '',
        multi_ac_code: '',
        desc: '',
        BIE: '',
        active_year: '',
      },
      rules: [
        {
          // 必填
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      years: [],
      funds: [],
      accounts: [],
      active_year_arr: [],
      BIE_arr: [],
      account_arr: [],
      selectAccountList: [],
      loading: true,
      dialogVisible: false,
      treeProps: {
        children: 'children',
        label: 'name_cn',
      },
      fund_id: '',
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
    BIE_list() {
      return ['B', 'I', 'E'].map(i => {
        return {
          label: i,
          value: i,
        }
      })
    },
  },
  watch: {
    editObject() {
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    validateACCode(rule, value, callback) {
      if (this.account_arr.length === 0) {
        callback(new Error(' '))
      } else {
        callback()
      }
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentStaffType(staffType, html, startLevel = 1) {
      let text = this.language === 'en' ? staffType.st_type_en : staffType.st_type_cn
      if (html) {
        text = '&nbsp;'.repeat((staffType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    checkRequired(rule, value, callback) {},
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          getDescription(this.editObject.desc_id)
            .then(res => {
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              this.BIE_arr = res.BIE ? res.BIE.split('') : []
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.BIE_arr = []
          this.active_year_arr = []
          this.form = Object.assign({}, this.defaultForm)
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editObject && res.length) {
            if (this.fyCode) {
              const selected = res.find(i => i.fy_code === this.fyCode)
              if (selected) {
                this.active_year_arr.push(selected.fy_code)
              }
            } else {
              if (this.currentYear && this.currentYear.fy_code) {
                const item = res.find(i => i.fy_code === this.currentYear.fy_code)
                if (item) {
                  this.active_year_arr.push(item.fy_code)
                }
              }
            }
          }
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.funds = res
          if (res.length && (this.fund_id = res[0].fund_id)) {
            return getAccountTree(res[0].fund_id)
          }
          return null
        })
        .then(res => {
          if (res) {
            this.account_arr = [this.defaultAccount]
            this.accounts = this.resetArrayChecked(res, [this.defaultAccount])
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const desc = this.form.desc
        // BIE
        const BIE_arr = this.BIE_arr
        BIE_arr.sort(function(a, b) {
          const order = ['B', 'I', 'E']
          return order.indexOf(a) - order.indexOf(b)
        })

        const BIE = BIE_arr.filter(i => i).join(',')
        const multi_ac_code = this.account_arr.filter(i => i).join(',')
        const active_year = this.active_year_arr.filter(i => i).join(',')

        // 新增
        createDescription({
          multi_ac_code,
          desc,
          BIE,
          active_year,
        })
          .then(() => {
            this.$message.success(this.$t('message.addSuccess'))
            this.$emit('onCancel', true)
          })
          .catch(() => {})
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    // 修改帳目類別，重新獲取會計科目
    changeFund(val) {
      if (val) {
        this.loadAccountTree(val)
      } else {
        this.preferences.filters.account = ''
      }
    },
    // 載入會計科目樹
    loadAccountTree(fund_id) {
      getAccountTree(fund_id).then(res => {
        this.accounts = this.resetArrayChecked(res, [])
      })
    },
    setAccount(checked, data) {
      data.checked = checked
      if (checked) {
        this.account_arr.push(data.code)
        this.selectAccountList.push(data)
      } else {
        const code_index = this.account_arr.indexOf(data.code)
        code_index > -1 && this.account_arr.splice(code_index, 1)
        const data_index = this.selectAccountList.indexOf(data)
        data_index > -1 && this.selectAccountList.splice(data_index, 1)
      }
    },
    removeAccount(row) {
      const code_index = this.account_arr.findIndex(i => i === row.code)
      code_index > -1 && this.account_arr.splice(code_index, 1)

      const data_index = this.selectAccountList.findIndex(i => i.code === row.code)
      data_index > -1 && this.selectAccountList.splice(data_index, 1)

      this.resetArrayChecked(this.accounts, this.account_arr)
      // this.$forceUpdate()
    },
    resetArrayChecked(item, arr) {
      for (let i = 0; i < item.length; i++) {
        this.resetChecked(item[i], arr)
      }
      return item
    },
    resetChecked(item, arr) {
      if (arr.indexOf(item.code) !== -1) {
        const data_index = this.selectAccountList.findIndex(i => i.code === item.code)
        if (data_index === -1) {
          this.selectAccountList.push(item)
        }
        item.checked = true
      } else {
        item.checked = false
      }

      if (item.children) {
        for (let i = 0; i < item.children.length; i++) {
          this.resetChecked(item.children[i], arr)
        }
      }
    },
  },
}
</script>

<style scoped>
.account-item {
  width: 100%;
  text-align: left;
}
#multi-ac-code /deep/ .el-form-item__content {
  height: auto !important;
}
</style>
