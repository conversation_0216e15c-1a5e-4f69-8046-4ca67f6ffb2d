import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // getToken from cookie
import { autoLogin } from '@/utils/autoLogin' // Authentication

NProgress.configure({ showSpinner: false }) // NProgress Configuration

// permission judge function
function hasPermission(permissions, p_code) {
  if (p_code === '*') {
    return true
  }
  return (
    permissions &&
    permissions[p_code] &&
    permissions[p_code].p_selected &&
    permissions[p_code].p_selected.includes('Y')
  )
}
function hasPermissionByPath2(routers, path, parent = '') {
  for (let i = 0; i < routers.length; i++) {
    const item = routers[i]

    const p = item.path[0] === '/' ? item.path : parent + '/' + item.path
    if (p === path) {
      return true
    }

    if (item.children && item.children.length > 0) {
      if (hasPermissionByPath2(item.children, path, p)) {
        return true
      }
    }
  }
  return false
}

const whiteList = ['/login', '/401', '/404', '/tutorial'] // no redirect whitelist
const schoolCodeKey = 'sc'
router.beforeEach(async(to, from, next) => {
  NProgress.start() // start progress bar
  // If from's query has code and to's query doesn't have code, carry from's code over
  const query = {}
  if (to.query[schoolCodeKey]) {
    query[schoolCodeKey] = to.query[schoolCodeKey]
  } else if (from.query[schoolCodeKey]) {
    query[schoolCodeKey] = from.query[schoolCodeKey]
  }
  if (from.query[schoolCodeKey] && !to.query[schoolCodeKey]) {
    to.query[schoolCodeKey] = from.query[schoolCodeKey]
    next({ name: to.name, query: { ...to.query }, params: { ...to.params }, replace: true })
  }
  if (query[schoolCodeKey] && !store.getters.schCode) {
    store.commit('SET_SCHOOL_CODE', query[schoolCodeKey])
  }
  await autoLogin(next)
  if (getToken()) {
    // determine if there has token
    /* has token*/
    if (store.getters.roles.length === 0) {
      // Check if current role has fetched user_info
      store
        .dispatch('GetUserInfo')
        .then(res => {
          // Fetch user_info
          const permissions = res.permissions // note: roles must be a array! such as: ['editor','develop']

          if (
            (typeof permissions === 'object' &&
              Object.getOwnPropertyNames(permissions).length === 0) ||
            (Array.isArray(permissions) && permissions.length === 0)
          ) {
            store.dispatch('FedLogOut').then(() => {
              next({ path: '/401', replace: true, query: { noGoBack: true } })
            })
            return
          }
          store.dispatch('GenerateRoutes', { permissions }).then(() => {
            // Generate accessible route table based on roles permissions
            const routers = store.getters.addRouters
            router.addRoutes(routers) // Dynamically add accessible route table
            console.log(routers)
            if (hasPermissionByPath2(routers, to.fullPath) && to.fullPath !== '/') {
              next(to)
            } else {
              // debugger
              store
                .dispatch('getLastPage')
                .then(page => {
                  next({ name: page, replace: true, query: { ...query } })
                })
                .catch(() => {
                  if (routers && routers[1] && routers[1].children && routers[1].children[0]) {
                    const name = routers[1].children[0].name
                    next({ name: name, query: { ...query } })
                  } else {
                    next({ path: '/', query: { ...query } })
                  }
                })
            }
          })
        })
        .catch(err => {
          store.dispatch('FedLogOut').then(() => {
            Message.error(err)
            next({ path: '/', query: { ...query } })
          })
        })
    } else {
      // If there's no need to dynamically change permissions, you can directly call next() and remove the permission check below ↓
      if (hasPermission(store.getters.permissions, to.meta.p_code)) {
        next()
      } else {
        if (store.getters.addRouters.length > 1 && store.getters.addRouters[1].children[0]) {
          const name = store.getters.addRouters[1].children[0].name
          next({ name: name, query: { ...query } })
        } else {
          next({ path: '/401', replace: true, query: { noGoBack: true, ...query } })
        }
      }
      // Can be deleted ↑
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // In the login-free whitelist, enter directly
      next()
    } else {
      let sc = ''
      if (query[schoolCodeKey]) {
        sc = '&' + schoolCodeKey + '=' + query[schoolCodeKey]
      }
      next(`/login?redirect=${to.path}${sc}`) // Otherwise redirect all to login page
      NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
