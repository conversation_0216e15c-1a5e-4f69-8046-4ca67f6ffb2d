<template>
  <div ref="page" v-loading="loading" :class="className">
    <el-row style="margin-bottom: 5px">
      <el-col :span="18" class="filter">
        <el-row>
          <span v-if="showTitle" style="padding-right: 20px">
            <div class="enquiry-title">
              <i class="edac-icon edac-icon-Search view-icon" />
              <span class="view-title">{{ $t('router.enquiryGeneral') }}</span>
            </div>
          </span>
          <span style="vertical-align: bottom">
            <el-button type="primary" size="mini" class="action-button" @click="reloadData">{{
              $t('enquiry.general.button.fetch')
            }}</el-button>
            <el-button type="info" size="mini" class="action-button" @click="resetParams">{{
              $t('enquiry.general.button.reset')
            }}</el-button>
          </span>
        </el-row>
      </el-col>
      <el-col :span="6">
        <div class="actions-icon">
          <i
            class="edac-icon action-icon edac-icon-setting1"
            :title="$t('btnTitle.pageSetting')"
            @click="onSetting"
          />
          <i
            v-if="allPermission || hasPermission_Output"
            class="edac-icon action-icon edac-icon-excel"
            :title="$t('btnTitle.exportExcelPage')"
            @click="onExport('PAGE')"
          />
          <i
            v-if="allPermission || hasPermission_Output"
            class="edac-icon action-icon edac-icon-excel_add"
            :title="$t('btnTitle.exportExcelAll')"
            @click="onExport('ALL')"
          />
        </div>
      </el-col>
    </el-row>
    <div ref="filters">
      <!-- 篩選控制 -->
      <el-row class="filter-controls">
        <el-button
          :class="preferences.filters.show_date ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_date = !preferences.filters.show_date"
        >
          {{ $t('enquiry.general.label.date') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_fund ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_fund = !preferences.filters.show_fund"
        >
          {{ $t('enquiry.general.label.fund') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_type ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_type = !preferences.filters.show_type"
        >
          {{ $t('enquiry.general.label.type') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_payee ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_payee = !preferences.filters.show_payee"
        >
          {{ $t('enquiry.general.label.payee') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_ref ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_ref = !preferences.filters.show_ref"
        >
          {{ $t('enquiry.general.label.ref') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_contra ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_contra = !preferences.filters.show_contra"
        >
          {{ $t('enquiry.general.label.contra') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_amount ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_amount = !preferences.filters.show_amount"
        >
          {{ $t('enquiry.general.label.amount') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_quote ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_quote = !preferences.filters.show_quote"
        >
          {{ $t('enquiry.general.label.quote') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_desc ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_desc = !preferences.filters.show_desc"
        >
          {{ $t('enquiry.general.label.desc') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_vc_code ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_vc_code = !preferences.filters.show_vc_code"
        >
          {{ $t('enquiry.general.label.vc_code') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_budget ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_budget = !preferences.filters.show_budget"
        >
          {{ $t('enquiry.general.label.budget') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_dept ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_dept = !preferences.filters.show_dept"
        >
          {{ $t('enquiry.general.label.dept') }}
        </el-button>
        <el-button
          :class="preferences.filters.show_staff ? '' : 'disable'"
          size="mini"
          @click="preferences.filters.show_staff = !preferences.filters.show_staff"
        >
          {{ $t('enquiry.general.label.staff') }}
        </el-button>
      </el-row>
      <el-form label-width="60px" class="filter-form">
        <!-- 日期 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_date" class="filter-row">
            <div class="filter-time">
              <el-form-item :label="$t('enquiry.general.label.date')">
                <el-select
                  v-model="preferences.filters.date_type"
                  class="time-type"
                  @change="onTimeType"
                >
                  <el-option :label="$t('enquiry.timeType.all')" value="A" />
                  <el-option :label="$t('enquiry.timeType.year')" value="Y" />
                  <el-option :label="$t('enquiry.timeType.to')" value="T" />
                  <el-option :label="$t('enquiry.timeType.month')" value="M" />
                  <el-option :label="$t('enquiry.timeType.free')" value="F" />
                  <el-option :label="$t('enquiry.timeType.day')" value="D" />
                </el-select>
                <el-select
                  v-if="'YMT'.includes(preferences.filters.date_type)"
                  v-model="preferences.filters.date_fy_id"
                  class="year"
                  @change="onChangeYear"
                >
                  <el-option
                    v-for="item in yearList"
                    :key="item.fy_id"
                    :label="item.fy_name"
                    :value="item.fy_id"
                  />
                </el-select>
                <el-select
                  v-if="'MT'.includes(preferences.filters.date_type)"
                  v-model="preferences.filters.date_pd_code"
                  class="month"
                  @change="onChangeMonth"
                >
                  <!--                <el-option-->
                  <!--                  label="&#45;&#45;/&#45;&#45;&#45;&#45;"-->
                  <!--                  value=""-->
                  <!--                />-->
                  <el-option
                    v-for="item in monthList"
                    :key="item.pd_id"
                    :label="item.pd_name"
                    :value="item.pd_code"
                  />
                </el-select>
                <el-date-picker
                  v-show="preferences.filters.date_type === 'F'"
                  v-model="dateRange"
                  :start-placeholder="$t('enquiry.general.label.startDate')"
                  :end-placeholder="$t('enquiry.general.label.endDate')"
                  :format="styles.dateFormat"
                  unlink-panels
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  style="width: 220px; vertical-align: middle"
                  @change="onChangeDateRange"
                />
                <el-date-picker
                  v-show="preferences.filters.date_type === 'D'"
                  v-model="preferences.filters.date_date"
                  :placeholder="$t('placeholder.selectDate')"
                  :format="styles.dateFormat"
                  value-format="yyyy-MM-dd"
                  align="right"
                  type="date"
                  style="width: 150px; vertical-align: middle"
                />
                <!--@change="reloadData"-->
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 篩選-賬目 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_fund" class="filter-row">
            <el-row class="account-filter">
              <el-form-item :label="$t('enquiry.general.label.fund')">
                <el-select
                  v-model="preferences.filters.fund_id"
                  class="fund"
                  @change="onChangeFund"
                >
                  <!--          @change="changeFund"-->
                  <el-option
                    v-for="item in fundList"
                    :key="item.fund_id"
                    :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
                    :value="item.fund_id"
                  />
                </el-select>
                <SelectTree
                  v-model="preferences.filters.fund_ac_code"
                  :options="accountTree"
                  :props="{
                    label: language === 'en' ? 'name_en' : 'name_cn',
                    value: 'code',
                    code: 'code',
                    group_id: 'fund_id',
                    value_id: 'account_id',
                    children: 'children',
                  }"
                  :group-id.sync="preferences.filters.fund_ac_fund_id"
                  :select-group.sync="preferences.filters.fund_selectGroup"
                  :show-all-option="true"
                  :all-option-text="$t('enquiry.general.label.allAccountType')"
                  style="width: 266px"
                  @change="onChangeAccount"
                />
              </el-form-item>
            </el-row>
          </el-row>
        </transition>
        <!-- 金額 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_amount" class="filter-row">
            <div class="filter-amount">
              <el-form-item :label="$t('enquiry.general.label.amount')">
                <el-select
                  v-model="preferences.filters.amount_sbet"
                  class="time-type"
                  style="width: 95px"
                >
                  <el-option :label="$t('enquiry.general.amountType.bet')" value="BET" />
                  <el-option :label="$t('enquiry.general.amountType.val')" value="VAL" />
                  <el-option :label="$t('enquiry.general.amountType.gt')" value="GT" />
                  <el-option :label="$t('enquiry.general.amountType.lt')" value="LT" />
                </el-select>
                <div style="display: inline-block">
                  <ENumeric
                    v-model="preferences.filters.amount_from"
                    style="width: 80px"
                    @keyup.enter.native="reloadData"
                  />
                  <span v-if="'BET' === preferences.filters.amount_sbet">-</span>
                  <ENumeric
                    v-if="'BET' === preferences.filters.amount_sbet"
                    v-model="preferences.filters.amount_to"
                    style="width: 80px"
                    @keyup.enter.native="reloadData"
                  />
                </div>
                <el-select
                  v-model="preferences.filters.amount_type"
                  class="time-type"
                  style="width: 117px"
                >
                  <el-option label="±Dr/±Cr" value="A" />
                  <el-option label="+Dr/+Cr" value="B" />
                  <el-option label="+Dr/-Cr" value="C" />
                  <el-option label="-Dr/+Cr" value="D" />
                  <el-option label="-Dr/-Cr" value="E" />
                  <el-option label="±Dr" value="F" />
                  <el-option label="+Dr" value="G" />
                  <el-option label="-Dr" value="H" />
                  <el-option label="±Cr" value="I" />
                  <el-option label="+Cr" value="J" />
                  <el-option label="-Cr" value="K" />
                </el-select>
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 類別 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_type" class="filter-row">
            <div class="filter-time">
              <el-form-item :label="$t('enquiry.general.label.type')">
                <el-select
                  v-model="preferences.filters.vc_type"
                  class="time-type"
                  @change="onTimeType"
                >
                  <el-option :label="$t('enquiry.general.vc_type.all')" value="" />
                  <el-option :label="$t('enquiry.general.vc_type.i')" value="I" />
                  <el-option :label="$t('enquiry.general.vc_type.e')" value="E" />
                </el-select>
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 收付款 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_payee" class="filter-row">
            <div class="filter-time">
              <el-form-item :label="$t('enquiry.general.label.payee')">
                <el-input
                  v-model="preferences.filters.payee_name"
                  @keyup.enter.native="reloadData"
                />
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 參考號 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_ref" class="filter-row">
            <div class="filter-time">
              <el-form-item :label="$t('enquiry.general.label.ref')">
                <el-input v-model="preferences.filters.ref" @keyup.enter.native="reloadData" />
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 對沖號 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_contra" class="filter-row">
            <div class="filter-time">
              <el-form-item :label="$t('enquiry.general.label.contra')">
                <el-input
                  v-model="preferences.filters.contra_code"
                  @keyup.enter.native="reloadData"
                />
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 報價 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_quote" class="filter-row">
            <div class="filter-time">
              <el-form-item :label="$t('enquiry.general.label.quote')">
                <el-select
                  v-model="preferences.filters.quote_type"
                  class="time-type"
                  @change="onChangeQuote"
                >
                  <el-option label="N" value="" />
                  <el-option label="Q" value="Q" />
                  <el-option label="T" value="T" />
                </el-select>
                <el-select
                  v-if="preferences.filters.quote_type === 'Q'"
                  v-model="preferences.filters.quote_qnum"
                  class="time-type"
                >
                  <el-option v-for="i in 4" :key="i" :label="i - 1" :value="i - 1" />
                </el-select>
                <ENumeric
                  v-if="preferences.filters.quote_type === 'T'"
                  v-model="preferences.filters.quote_qnum"
                  style="width: 80px"
                />
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 內容 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_desc" class="filter-row">
            <div class="filter-time">
              <el-form-item :label="$t('enquiry.general.label.desc')">
                <el-input v-model="preferences.filters.descr" @keyup.enter.native="reloadData" />
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 傳票號 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_vc_code" class="filter-row">
            <div class="filter-time">
              <el-form-item :label="$t('enquiry.general.label.vc_code')">
                <el-input v-model="preferences.filters.vc_code" @keyup.enter.native="reloadData" />
              </el-form-item>
            </div>
          </el-row>
        </transition>
        <!-- 預算 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_budget" class="filter-row">
            <el-row class="account-filter">
              <el-form-item :label="$t('enquiry.general.label.budget')">
                <el-select
                  v-model="preferences.filters.date_fy_id"
                  class="year"
                  @change="onChangeYear"
                >
                  <el-option
                    v-for="item in yearList"
                    :key="item.fy_id"
                    :label="item.fy_name"
                    :value="item.fy_id"
                  />
                </el-select>
                <BudgetSelectTreeVue
                  :data="budgetsTree"
                  :budget-id.sync="preferences.filters.budget_id"
                  :disabled-method="handleBudgetSelectDisabledMethod"
                  :language="language"
                  style="width: 265px"
                />
              </el-form-item>
            </el-row>
          </el-row>
        </transition>
        <!-- 部門 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_dept" class="filter-row">
            <el-row class="account-filter">
              <el-form-item :label="$t('enquiry.general.label.dept')">
                <SelectTree
                  v-model="preferences.filters.dept_code"
                  :options="departmentsTree"
                  :props="{
                    label: language === 'en' ? 'name_en' : 'name_cn',
                    value: 'code',
                    code: 'code',
                    group_id: 'dept_type_id',
                    value_id: 'department_id',
                    children: 'children',
                  }"
                  :group-id.sync="preferences.filters.dept_type_id"
                  :select-group.sync="preferences.filters.dept_selectGroup"
                  :show-all-option="true"
                  :all-option-text="$t('enquiry.general.label.allDept')"
                  style="width: 266px"
                />
              </el-form-item>
            </el-row>
          </el-row>
        </transition>
        <!-- 職員 -->
        <transition name="el-zoom-in-top">
          <el-row v-show="preferences.filters.show_staff" class="filter-row">
            <el-row class="account-filter">
              <el-form-item :label="$t('enquiry.general.label.staff')">
                <SelectTree
                  v-model="preferences.filters.st_code"
                  :options="staffTree"
                  :props="{
                    label: language === 'en' ? 'name_en' : 'name_cn',
                    value: 'code',
                    code: 'code',
                    group_id: 'st_type_id',
                    value_id: 'staff_id',
                    children: 'children',
                  }"
                  :group-id.sync="preferences.filters.st_type_id"
                  :select-group.sync="preferences.filters.staff_selectGroup"
                  :show-all-option="true"
                  :all-option-text="$t('enquiry.general.label.allStaff')"
                  style="width: 266px"
                />
              </el-form-item>
            </el-row>
          </el-row>
        </transition>
      </el-form>
    </div>
    <!-- 數據顯示 Table -->
    <el-row class="table-row">
      <b-table
        ref="table"
        :style-columns="styleColumns"
        :lang-key="langKey"
        :data="tableData"
        :show-actions="true"
        :actions-min-width="5"
        :auto-height="false"
        :sortable="true"
        :highlight-current-row="rowSelected"
        :amount-columns="amountColumns"
        :height="tableHeight"
        action-label=" "
        border
        @changeWidth="changeColumnWidth"
        @cell-click="onRowClick"
      >
        <template slot="columns">
          <vxe-table-column
            v-for="item in columnList"
            :key="item.ss_key"
            :title="$t(langKey + $refs.table.column_label(item))"
            :align="item.alignment"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :field="$refs.table.column_property(item)"
            :column-key="item.ss_key"
            :params="{ key: item.ss_key }"
            :formatter="$refs.table.formatter"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <!--<span v-if="amountColumns.includes(item.ss_key)">{{ scope.row[item.ss_key] }}</span>-->
              <span>{{
                $refs.table.customFormatter(
                  item.ss_key,
                  scope.row[$refs.table.column_property(item)],
                  customDateFormat
                )
              }}</span>
            </template>
          </vxe-table-column>
        </template>
      </b-table>
      <!--

        :span-method="spanMethod"
        :summary-method="summaryMethod"
        :show-summary="true"
        sum-text="總額"
        @row-click="onRowClick"
        @changeWidth="changeColumnWidth"

-->
    </el-row>
    <!-- 自定義樣式 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      :ss_code="ss_code"
      :has-theme="true"
      table-type="full-screen-without-first-field"
      @reloadStyleSheets="loadUserStyle(true)"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import FilterTime from './FilterTime'
import SelectTree from '@/components/SelectTree'
import ENumeric from '@/components/ENumeric'
import BTable from '@/components/BTable'
import TreeSelect from '@riophae/vue-treeselect'
import BudgetSelectTreeVue from '@/views/budget/common/BudgetSelectTreeVue.vue'

import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import enquiryComponentProxy from './enquiryComponentProxy'

import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// API
import { enquiryCondition } from '@/api/enquiry/condition'
import { getBudgetYears, getYear, fetchYears } from '@/api/master/years'
import { fetchFunds } from '@/api/master/funds' // 撥款
import { getAccountTree } from '@/api/master/account' // 撥款
import { getDepartmentsTree } from '@/api/assistance/department'
import { getStaffsTree } from '@/api/assistance/staff'
import { enquiryConditionExport } from '@/api/report/excel'
import { fetchBudgetTree } from '@/api/budget'
import { exportExcel } from '@/utils/excel'
import { getTreeBudgetById } from '@/views/budget/common/utils'
import { toDecimal } from '@/utils'
import { listenTo } from '@/utils/resizeListen'

export default {
  name: 'EnquiryGeneral',
  components: {
    FilterTime,
    BTable,
    customStyle,
    TreeSelect,
    SelectTree,
    ENumeric,
    BudgetSelectTreeVue,
  },
  mixins: [loadCustomStyle, mixinPermission, loadPreferences, enquiryComponentProxy],
  props: {
    showTitle: {
      type: Boolean,
      default: true,
    },
    allPermission: {
      type: Boolean,
      default: false,
    },
    jump: {
      type: Boolean,
      default: true,
    },
    rowSelected: {
      type: Boolean,
      default: true,
    },
    className: {
      type: String,
      default: 'enquiry-general',
    },
  },
  data() {
    return {
      ss_code: 'ac.enquiry.general',
      pf_code: 'ac.enquiry.general',
      p_code: 'ac.enquiry.general',
      manualLoadStyle: true,
      p_isComponent: true,

      loading: false,
      tableColumns: [
        'vc_date', // 日期
        'vc_no', // 傳票編號
        'ac_code', // 會計賬號
        'ac_name_', // 賬目名稱
        'descr', // 內容描述
        'tx_type', // IE
        'amount_dr', // 借方金額
        'amount_cr', // 貸方金額
        'net_amount', // 淨金額
        'vc_payee', // 收付款
        'ref', // 參考號
        'budget_code', // 預算名稱
        'vc_dept', // 部門編號
        'dept_name_', // 部門名稱
        'st_code', // 職員編號
        'staff_name_', // 職員名稱
        'vc_contra', // 對沖編號
      ],
      amountColumns: [
        'amount_dr', // 借方金額
        'amount_cr', // 貸方金額
        'net_amount', // 淨金額
      ],
      langKey: 'enquiry.general.label.',

      showDialog: false,

      tableData: [],

      preferences: {
        filters: {
          // date
          date_type: 'A', // 日期:A=所有,Y=全年,T=年度截至,M=當月,F=自由輸入,D=日期
          date_fy_id: '', // 日期:週期ID
          date_fy_code: '', // 日期:週期編號
          date_pd_code: '', // 日期:週期月份編號
          date_date_from: '', // 日期:日期範圍from
          date_date_to: '', // 日期:日期範圍to
          date_date: '', // 日期:日期
          // ac
          fund_ac_code: '', // 賬目:歸屬會計科目樹-會計科目code
          fund_id: '', // 賬目:賬目類別id
          fund_ac_fund_id: '', // 賬目:賬目類別id
          fund_selectGroup: false, // 賬目:
          // type
          vc_type: '',
          // type_ac_I: '', // 類別:收入類,可填N或Y
          // type_ac_E: '', // 類別:支出類,可填N或Y
          // payee
          payee_name: '', // 收付款:收款人(公司)描述
          // 參考號
          ref: '', // 參考號:參考號
          // 對沖號
          contra_code: '', // 對沖號:對沖編號
          // 報價
          quote_type: '', // 報價:Q(Quotation)或T(Tender)
          quote_qnum: '', // 報價:quote=Q(0,1,2,3)|quote=T(自由輸入)
          // 內容
          descr: '', // 內容:會計科目描述
          vc_code: '', // 傳票號:傳票編號
          // 職員
          st_code: '', // 職員:職員編號
          st_type_id: '', // 職員:職員類別id
          staff_selectGroup: false, // 職員:
          // 預算
          budget_id: '',
          // 部門
          dept_code: '', // 部門:部門編號
          dept_type_id: '', // 部門:部門類別id
          dept_selectGroup: false, // 部門:
          // 金額
          amount_sbet: 'BET', // 金額: BET=範圍,VAL=數值,GT=大於,LT=小於
          amount_from: '', // 金額:sbet=BET時使用
          amount_to: '', // 金額:sbet=BET時使用
          amount_amount: '', // 金額:sbet=VAL或sbet=GT或sbet=LT時使用
          amount_type: 'A', // 金額:A=±Dr/±Cr,B=+Dr/+Cr,C=+Dr/-Cr,D=-Dr/+Cr,E=-Dr/-Cr,F=±Dr,G=+Dr,H=-Dr,I=±Cr,J=+Cr,K=-Cr

          // 顯示篩選
          show_date: false,
          show_fund: false,
          show_type: false,
          show_payee: false,
          show_ref: false,
          show_contra: false,
          show_amount: false,
          show_quote: false,
          show_desc: false,
          show_vc_code: false,
          show_budget: false,
          show_staff: false,
          show_dept: false,
        },
      },
      customDateFormat: 'dd/MM/yy',
      yearList: [],
      monthList: [],
      dateRange: [],
      fundList: [],
      accountTree: [],
      budgetsTree: [],
      departmentsTree: [],
      staffTree: [],

      // 窗口適應
      resizeListen: {},
      filtersHeight: 30,
      pageResizeListen: {},
      pageHeight: 500,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'system', 'user_id']),
    allStaff() {
      return {
        active_year: '',
        children: null,
        code: '',
        level: 1,
        name_cn: this.$t('assistance.department.label.parent_default'),
        name_en: this.$t('assistance.department.label.parent_default'),
        parent_dept_type_id: null,
        seq: 0,
        st_grade: null,
        dept_type_id: null,
        staff_id: null,
        username: null,
      }
    },
    natureClass() {
      return this.preferences.filters.nature ? ' selected' : ''
    },
    iClass() {
      return (
        'edac-icon action-icon edac-icon-I' +
        (this.preferences.filters.ac_I === 'Y' ? '' : ' disable')
      )
    },
    eClass() {
      return (
        'edac-icon action-icon edac-icon-E' +
        (this.preferences.filters.ac_E === 'Y' ? '' : ' disable')
      )
    },
    bClass() {
      return (
        'edac-icon action-icon edac-icon-B' +
        (this.preferences.filters.ac_B === 'Y' ? '' : ' disable')
      )
    },
    timeTypeList() {
      return [
        {
          label: this.$t('enquiry.timeType.all'),
          value: 'A',
        },
        {
          label: this.$t('enquiry.timeType.year'),
          value: 'Y',
        },
        {
          label: this.$t('enquiry.timeType.to'),
          value: 'T',
        },
        {
          label: this.$t('enquiry.timeType.month'),
          value: 'M',
        },
        {
          label: this.$t('enquiry.timeType.free'),
          value: 'F',
        },
        {
          label: this.$t('enquiry.timeType.day'),
          value: 'D',
        },
      ]
    },
    columnList() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    tableHeight() {
      const h = this.pageHeight - this.filtersHeight - 33
      return h < 100 ? 100 : h
    },
  },
  watch: {},
  created() {
    switch (this.system) {
      case 'AC':
        this.ss_code = 'ac.enquiry.general'
        this.pf_code = 'ac.enquiry.general'
        this.p_code = 'ac.enquiry.general'
        break
      case 'BG':
        this.ss_code = 'bg.enquiry.general'
        this.pf_code = 'bg.enquiry.general'
        this.p_code = 'bg.enquiry.general'
        break
    }
    this.loadUserStyle()
  },
  beforeDestroy() {},
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.resizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
    })
    this.initData()
  },
  methods: {
    onSetting() {
      this.showDialog = true
    },
    normalizer(node) {
      console.log(node.dept_type_id, node.code)
      return {
        id: node.code,
        label:
          (node.dept_type_id ? '' : node.code ? `[${node.code}]` : '') +
          (this.language === 'en' ? node.name_en : node.name_cn),
        children: node.children,
      }
    },
    initData() {
      //
      this.loading = true
      this.loadUserPreference()
        .then(() => {
          this.preferences.filters.descr = ''
          this.preferences.filters.vc_code = ''
          this.preferences.filters.ref = ''
          this.preferences.filters.contra_code = ''
          this.preferences.filters.payee_name = ''
          this.preferences.filters.amoun_from = ''
          this.preferences.filters.amount_to = ''
        })
        .then(this.loadYear)
        .then(() => {
          if (this.yearList.length) {
            // const year = this.yearList.find(i => i.fy_code === this.preferences.filters.selectedYear)
            // if (year) {
            //   return
            // }
            const oY = this.yearList.find(i => i.fy_id === this.preferences.filters.date_fy_id)
            if (oY) {
              this.preferences.filters.date_fy_id = oY.fy_id
              this.preferences.filters.date_fy_code = oY.fy_code
            } else {
              this.preferences.filters.date_fy_id = this.yearList[0].fy_id
              this.preferences.filters.date_fy_code = this.yearList[0].fy_code
            }
          } else {
            return Promise.reject(this.$t('message.theYearDoNotExist'))
          }
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.fundList = res
          if (res.length) {
            let fund_id = this.preferences.filters.fund_id
            if (!fund_id) {
              fund_id = res[0].fund_id
              this.preferences.filters.fund_id = fund_id
            }
            return this.loadAccountTree(fund_id)
          } else {
            return Promise.reject()
          }
        })
        .then(this.loadBudgetsTree) // 部門樹
        .then(this.loadDepartmentsTree) // 部門樹
        .then(this.loadStaffTree) // 職員樹
        .catch(() => {
          //
        })
        .finally(() => {
          this.loading = false
        })
      // .then(this.reloadData)
    },
    resetParams() {
      const filters = this.preferences.filters
      this.preferences.filters = {
        // date
        date_type: 'A', // 日期:A=所有,Y=全年,T=年度截至,M=當月,F=自由輸入,D=日期
        date_fy_id: '', // 日期:週期ID
        date_fy_code: '', // 日期:週期編號
        date_pd_code: '', // 日期:週期月份編號
        date_date_from: '', // 日期:日期範圍from
        date_date_to: '', // 日期:日期範圍to
        date_date: '', // 日期:日期
        // ac
        fund_ac_code: '', // 賬目:歸屬會計科目樹-會計科目code
        fund_id: filters.fund_id, // 賬目:賬目類別id
        fund_ac_fund_id: '', // 賬目:賬目類別id
        fund_selectGroup: false, // 賬目:
        // type
        vc_type: '',
        // type_ac_I: '', // 類別:收入類,可填N或Y
        // type_ac_E: '', // 類別:支出類,可填N或Y
        // payee
        payee_name: '', // 收付款:收款人(公司)描述
        // 參考號
        ref: '', // 參考號:參考號
        // 對沖號
        contra_code: '', // 對沖號:對沖編號
        // 報價
        quote_type: '', // 報價:Q(Quotation)或T(Tender)
        quote_qnum: '', // 報價:quote=Q(0,1,2,3)|quote=T(自由輸入)
        // 內容
        descr: '', // 內容:會計科目描述
        vc_code: '', // 傳票號:傳票編號
        // 職員
        st_code: '', // 職員:職員編號
        st_type_id: '', // 職員:職員類別id
        staff_selectGroup: false, // 職員:
        // 預算
        budget_id: '',
        // 部門
        dept_code: '', // 部門:部門編號
        dept_type_id: '', // 部門:部門類別id
        dept_selectGroup: false, // 部門:
        // 金額
        amount_sbet: 'BET', // 金額: BET=範圍,VAL=數值,GT=大於,LT=小於
        amount_from: '', // 金額:sbet=BET時使用
        amount_to: '', // 金額:sbet=BET時使用
        amount_amount: '', // 金額:sbet=VAL或sbet=GT或sbet=LT時使用
        amount_type: 'A', // 金額:A=±Dr/±Cr,B=+Dr/+Cr,C=+Dr/-Cr,D=-Dr/+Cr,E=-Dr/-Cr,F=±Dr,G=+Dr,H=-Dr,I=±Cr,J=+Cr,K=-Cr

        // 顯示篩選
        show_date: false,
        show_fund: false,
        show_type: false,
        show_payee: false,
        show_ref: false,
        show_contra: false,
        show_amount: false,
        show_quote: false,
        show_desc: false,
        show_vc_code: false,
        show_budget: false,
        show_staff: false,
        show_dept: false,
      }
      this.dateRange = ['', '']
      this.tableData = []
    },
    // 獲取數據
    fetchData() {
      this.reloadData()
    },
    reloadData() {
      this.loading = true
      const filters = this.preferences.filters
      let type = filters.date_type
      // 初始化所有參數，不提交的不用賦值
      let fy_code,
        pd_code,
        date_from,
        date_to,
        date,
        payee,
        ac_code,
        fund_id,
        ac_I,
        ac_E,
        ref,
        contra_cde,
        quote,
        qnum,
        descr,
        vc_code,
        st_code,
        st_type_id,
        budget_id,
        dept_code,
        dept_type_id,
        sbet,
        amount_from,
        amount_to,
        amount,
        amount_type
      /* ----- 日期查詢 ----- */
      if (!filters.show_date) {
        type = 'A'
      }
      switch (type) {
        case 'A':
          break
        case 'Y':
          fy_code = filters.date_fy_code
          break
        case 'T':
          fy_code = filters.date_fy_code
          pd_code = filters.date_pd_code
          break
        case 'M':
          fy_code = filters.date_fy_code
          pd_code = filters.date_pd_code
          break
        case 'F':
          if (Array.isArray(this.dateRange) && this.dateRange.length === 2) {
            const date_start = this.dateRange[0]
            const date_end = this.dateRange[1]
            date_from = date_start
            date_to = date_end
          }
          break
        case 'D':
          date = filters.date_date
          break
      }
      /* ----- 賬目 ----- */
      if (filters.show_fund) {
        if (filters.fund_ac_fund_id) {
          fund_id = filters.fund_ac_fund_id
        } else {
          fund_id = filters.fund_id
          ac_code = filters.fund_ac_code ? filters.fund_ac_code : undefined
        }
        // fund_id = filters.fund_id
        // ac_code = filters.fund_ac_code ? filters.fund_ac_code : undefined
      }
      /* ----- 類別 ----- */
      if (filters.show_type) {
        switch (filters.vc_type) {
          case 'I':
            ac_I = 'Y'
            ac_E = 'N'
            break
          case 'E':
            ac_I = 'N'
            ac_E = 'Y'
            break
          default:
            ac_I = 'N'
            ac_E = 'N'
            break
        }
      }
      /* ----- 收付款 ----- */
      if (filters.show_payee) {
        payee = filters.payee_name
      }
      /* ----- 參考號 ----- */
      if (filters.show_ref) {
        ref = filters.ref
      }
      /* ----- 對沖號 ----- */
      if (filters.show_contra) {
        contra_cde = filters.contra_code
      }
      /* ----- 金額 ----- */
      if (filters.show_amount) {
        sbet = filters.amount_sbet
        amount_type = filters.amount_type
        switch (sbet) {
          case 'BET':
            amount_from = filters.amount_from
            amount_to = filters.amount_to
            break
          case 'VAL':
          case 'GT':
          case 'LT':
            amount = filters.amount_from
            break
        }
      }
      /* ----- 報價 ----- */
      if (filters.show_quote) {
        quote = filters.quote_type
        qnum = filters.quote_qnum
      }
      /* ----- 內容 ----- */
      if (filters.show_desc) {
        descr = filters.descr
      }
      /* ----- 傳票號 ----- */
      if (filters.show_vc_code) {
        vc_code = filters.vc_code
      }
      /* ----- 預算 ----- */
      if (filters.show_budget) {
        fy_code = filters.date_fy_code
        if (filters.budget_id) {
          budget_id = filters.budget_id
        }
      }
      /* ----- 部門 ----- */
      if (filters.show_dept) {
        if (filters.dept_type_id) {
          dept_type_id = filters.dept_type_id
        } else {
          dept_code = filters.dept_code
        }
      }
      /* ----- 職員 ----- */
      if (filters.show_staff) {
        if (filters.st_type_id) {
          st_type_id = filters.st_type_id
        } else {
          st_code = filters.st_code
        }
      }

      enquiryCondition({
        type,
        fy_code,
        pd_code,
        date_from,
        date_to,
        date,
        payee,
        ac_code,
        fund_id,
        ac_I,
        ac_E,
        ref,
        contra_cde,
        quote,
        qnum,
        descr,
        vc_code,
        st_code,
        st_type_id,
        budget_id,
        dept_code,
        dept_type_id,
        sbet,
        amount_from,
        amount_to,
        amount,
        amount_type,
      })
        .then(res => {
          res.result.forEach(item => {
            let cr = toDecimal(Number(item.amount_cr))
            if (isNaN(cr)) {
              cr = 0
            }
            let dr = toDecimal(Number(item.amount_dr))
            if (isNaN(dr)) {
              dr = 0
            }
            item.net_amount = toDecimal(cr - dr)
          })
          this.tableData = res.result
        })
        .finally(() => {
          this.loading = false
        })
    },
    // date
    loadYear() {
      return new Promise((resolve, reject) => {
        const api = this.$route.name === 'budgetDailyEnquires' ? getBudgetYears : fetchYears
        api()
          .then(res => {
            this.yearList = res
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    loadMonth(fy_id) {
      if (!fy_id) return Promise.resolve()
      return new Promise((resolve, reject) => {
        getYear(fy_id)
          .then(res => {
            this.monthList = res.periods
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    setYear() {
      if (!this.preferences.filters.date_fy_code && this.yearList.length) {
        const year = this.yearList.find(i => i.fy_id === this.preferences.filters.date_fy_id)
        if (year) {
          this.preferences.filters.date_fy_code = year.fy_code
          this.preferences.filters.date_fy_id = year.fy_id
        } else {
          this.preferences.filters.date_fy_code = this.yearList[0].fy_code
          this.preferences.filters.date_fy_id = this.yearList[0].fy_id
        }
      }
    },
    async onTimeType(val) {
      switch (val) {
        case 'A':
          this.preferences.filters.date_fy_id = ''
          this.preferences.filters.date_fy_code = ''
          this.preferences.filters.date_pd_code = ''
          this.preferences.filters.date_date = ''
          this.preferences.filters.date_date_from = ''
          this.preferences.filters.date_date_to = ''
          // this.reloadData()
          break
        case 'Y':
          this.setYear()
          this.preferences.filters.date_pd_code = ''
          // this.reloadData()
          break
        case 'T':
        case 'M':
          this.setYear()
          this.loadMonth(this.preferences.filters.date_fy_id, true).then(() => {
            if (this.monthList.length) {
              const item = this.monthList[0]
              this.preferences.filters.date_pd_code = item.pd_code
              // this.reloadData()
            }
          })
          break
      }
    },
    clearDate(type) {
      switch (type) {
        case 1:
          break
      }
    },
    // 修改年份（會計週期）
    onChangeYear(val) {
      if (!val) {
        this.preferences.filters.date_fy_id = ''
        this.preferences.filters.date_fy_code = ''
        this.preferences.filters.date_pd_code = ''
        this.preferences.filters.date_date_to = ''
        this.preferences.filters.date_date_from = ''
        return
      }
      const item = this.yearList.find(i => i.fy_id === val)
      if (!item) this.$message.error(this.$t('message.pleaseReload'))
      this.preferences.filters.date_fy_code = item.fy_code

      this.loadMonth(val).then(res => {
        const periods = res.periods
        if (periods.length === 0) return

        if (this.preferences.filters.date_type === 'Y' || this.preferences.filters.date_pd_code) {
          // this.reloadData()
        }
        switch (this.preferences.filters.date_type) {
          case 'M':
          case 'T':
            this.preferences.filters.date_pd_code = periods[0].pd_code
            break
          default:
            this.preferences.filters.date_pd_code = ''
            break
        }
      })
      this.loadBudgetsTree()
    },
    // 修改月份
    onChangeMonth(val) {
      if (!val) return
      // this.reloadData()
    },
    onChangeDateRange(val) {
      console.log(val)
      if (Array.isArray(val) && val.length === 2) {
        this.preferences.filters.date_date_from = val[0]
        this.preferences.filters.date_date_to = val[1]
        // this.reloadData()
      } else {
        this.preferences.filters.date_date_from = ''
        this.preferences.filters.date_date_to = ''
      }
    },
    /* ------------------------------------------------------
      賬目
    -------------------------------------------------------- */
    // 會計科目樹
    loadAccountTree(fund_id) {
      return new Promise((resolve, reject) => {
        getAccountTree(fund_id)
          .then(res => {
            this.accountTree = res
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    onChangeFund(fund_id) {
      console.log('change fund_id', fund_id)
      this.loadAccountTree(fund_id)
    },
    onChangeAccount(ac_code, selectGroup) {},
    /* ------------------------------------------------------
      報價
    -------------------------------------------------------- */
    onChangeQuote(val) {
      switch (val) {
        case 'N':
          this.preferences.filters.quote_qnum = ''
          break
        case 'Q':
          if (![0, 1, 2, 3].includes(this.preferences.filters.quote_qnum)) {
            this.preferences.filters.quote_qnum = 0
          }
          break
        case 'T':
          this.preferences.filters.quote_qnum = 0
          break
      }
    },
    /* ------------------------------------------------------
      預算
    -------------------------------------------------------- */
    // 預算樹
    loadBudgetsTree() {
      return new Promise((resolve, reject) => {
        const fy_code = this.preferences.filters.date_fy_code
        if (this.yearList.findIndex(i => i.fy_code === fy_code) !== -1) {
          const type = 'C'
          fetchBudgetTree({ fy_code, type })
            .then(res => {
              const top = Object.assign(
                {},
                {
                  budget_id: '',
                  budget_code: '',
                  name_cn: this.$t(this.langKey + 'allBudget', 'zh-hk'),
                  name_en: this.$t(this.langKey + 'allBudget', 'en'),
                },
              )
              top.children = res
              this.budgetsTree = [top]
              if (this.preferences.filters.budget_id) {
                const b = getTreeBudgetById(this.budgetsTree, this.preferences.filters.budget_id)
                if (!b) {
                  this.preferences.filters.budget_id = ''
                }
              } else {
                this.preferences.filters.budget_id = ''
              }
              // this.preferences.filters.budget_id = '' // res.length ? res[0].budget_id : ''
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        }
      })
    },
    /* ------------------------------------------------------
      部門
    -------------------------------------------------------- */
    // 部門樹
    loadDepartmentsTree() {
      return new Promise((resolve, reject) => {
        getDepartmentsTree()
          .then(res => {
            this.departmentsTree = res
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    /* ------------------------------------------------------
      職員
    -------------------------------------------------------- */
    // 職員樹
    loadStaffTree() {
      return new Promise((resolve, reject) => {
        getStaffsTree()
          .then(res => {
            this.staffTree = res
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },

    onRowClick({ row }) {
      if (!this.jump) return
      if (!row.lg_id) return
      let routerName = ''
      switch (row.vt_category) {
        case 'P':
          routerName = 'dailyPaymentView'
          break
        case 'C':
          routerName = 'dailyPettyCashView'
          break
        case 'R':
          routerName = 'dailyReceiptView'
          break
        case 'T':
          routerName = 'dailyBankTransferView'
          break
        case 'J':
          routerName = 'dailyJournalView'
          break
        default:
          this.$message.error(this.$t('message.pleaseContactTheAdministrator'))
          return
      }

      this.$router.replace({
        name: routerName,
        force: true,
        params: {
          action: 'view',
          isView: true,
          parentFyCode: row.fy_code,
          parentFyId: row.fy_id,
          parentPdCode: row.pd_code,
          editObject: row,
          vt_category: row.vt_category,
          lg_id: row.lg_id,
          time: new Date().getTime(),
        },
        query: {
          t: new Date().getTime(),
        },
      })
      // 取消其他頁面選中
      this.handleThisPageClickRow()
    },

    onExport(export_type) {
      let user_id
      let staff_id
      switch (this.system) {
        case 'AC':
          user_id = this.user_id
          break
        case 'BG':
          staff_id = this.user_id
          break
      }
      const filters = this.preferences.filters
      const type = filters.date_type
      // 初始化所有參數，不提交的不用賦值
      let fy_code,
        pd_code,
        date_from,
        date_to,
        date,
        payee,
        ac_code,
        fund_id,
        ac_I,
        ac_E,
        ref,
        contra_cde,
        quote,
        qnum,
        descr,
        vc_code,
        st_code,
        st_type_id,
        dept_code,
        dept_type_id,
        sbet,
        amount_from,
        amount_to,
        amount,
        amount_type
      /* ----- 日期查詢 ----- */
      switch (type) {
        case 'A':
          break
        case 'Y':
          fy_code = filters.date_fy_code
          break
        case 'T':
          fy_code = filters.date_fy_code
          pd_code = filters.date_pd_code
          break
        case 'M':
          fy_code = filters.date_fy_code
          pd_code = filters.date_pd_code
          break
        case 'F':
          if (Array.isArray(this.dateRange) && this.dateRange.length === 2) {
            const date_start = this.dateRange[0]
            const date_end = this.dateRange[1]
            date_from = date_start
            date_to = date_end
          }
          break
        case 'D':
          date = filters.date_date
          break
      }
      /* ----- 賬目 ----- */
      if (filters.show_fund) {
        if (filters.fund_ac_fund_id) {
          fund_id = filters.fund_ac_fund_id
        } else {
          fund_id = filters.fund_id
          ac_code = filters.fund_ac_code ? filters.fund_ac_code : undefined
        }
        // fund_id = filters.fund_id
        // ac_code = filters.fund_ac_code ? filters.fund_ac_code : undefined
      }
      /* ----- 類別 ----- */
      if (filters.show_type) {
        switch (filters.vc_type) {
          case 'I':
            ac_I = 'Y'
            ac_E = 'N'
            break
          case 'E':
            ac_I = 'N'
            ac_E = 'Y'
            break
          default:
            ac_I = 'N'
            ac_E = 'N'
            break
        }
      }
      /* ----- 收付款 ----- */
      if (filters.show_payee) {
        payee = filters.payee_name
      }
      /* ----- 參考號 ----- */
      if (filters.show_ref) {
        ref = filters.ref
      }
      /* ----- 對沖號 ----- */
      if (filters.show_contra) {
        contra_cde = filters.contra_code
      }
      /* ----- 金額 ----- */
      if (filters.show_amount) {
        sbet = filters.amount_sbet
        amount_type = filters.amount_type
        switch (sbet) {
          case 'BET':
            amount_from = filters.amount_from
            amount_to = filters.amount_to
            break
          case 'VAL':
          case 'GT':
          case 'LT':
            amount = filters.amount_from
            break
        }
      }
      /* ----- 報價 ----- */
      if (filters.show_quote) {
        quote = filters.quote_type
        qnum = filters.quote_qnum
      }
      /* ----- 內容 ----- */
      if (filters.show_desc) {
        descr = filters.descr
      }
      /* ----- 傳票號 ----- */
      if (filters.show_vc_code) {
        vc_code = filters.vc_code
      }
      /* ----- 部門 ----- */
      if (filters.show_dept) {
        if (filters.dept_type_id) {
          dept_type_id = filters.dept_type_id
        } else {
          dept_code = filters.dept_code
        }
      }
      /* ----- 職員 ----- */
      if (filters.show_staff) {
        if (filters.st_type_id) {
          st_type_id = filters.st_type_id
        } else {
          st_code = filters.st_code
        }
      }
      this.loading = true
      enquiryConditionExport({
        user_id,
        staff_id,
        export_type,
        type,
        fy_code,
        pd_code,
        date_from,
        date_to,
        date,
        payee,
        ac_code,
        fund_id,
        ac_I,
        ac_E,
        ref,
        contra_cde,
        quote,
        qnum,
        descr,
        vc_code,
        st_code,
        st_type_id,
        dept_code,
        dept_type_id,
        sbet,
        amount_from,
        amount_to,
        amount,
        amount_type,
      })
        .then(res => exportExcel(res))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleBudgetSelectDisabledMethod(item) {
      return false
    },
  },
}
</script>

<style lang="scss" scoped>
$actionIconColor: #68afff;
$disableColor: #b9b6b6;
$settingColor: #b9b6b6;

.enquiry-general,
.enquiry-general-budget {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: scroll;
  position: relative;
  min-height: 100px;

  i.edac-icon {
    color: $actionIconColor;
    vertical-align: middle;
    font-size: 20px;

    &.disable {
      color: $disableColor;
    }
  }
  .filter-controls {
    margin: 3px 0;
    .el-button {
      margin: 1px 0;
      color: #ffffff;
      background-color: #3e97dd;
      padding: 7px 8px;
      &.disable {
        color: #707070;
        background-color: #ffffff;
      }
    }
  }
  .filter-form {
    -webkit-transition: height 0.6s;
    -moz-transition: height 0.6s;
    -o-transition: height 0.6s;
    transition: height 0.6s;
    padding-bottom: 5px;
    /deep/ {
      span.el-input__suffix-inner {
        vertical-align: middle;
      }
    }
  }
  .filter-row {
    padding: 1px 0;
    margin: 0;
    /deep/ {
      .el-form-item--medium .el-form-item__content,
      .el-form-item--medium .el-form-item__label {
        line-height: 25px;
      }
      .el-form-item {
        margin-bottom: 0 !important;
      }
      .el-select,
      .el-form-item__content > .el-input {
        width: 130px;
      }
      .filter-amount {
        /deep/ {
          .e-numeric {
            margin: 0;
          }
        }
      }

      .el-range-separator {
        line-height: 17px;
      }
    }
  }
  .filter {
    display: inline-block;
    .el-select {
      width: 150px;
    }
    .filter-time {
      .el-select {
        width: 100px;
      }
    }
    .action-button {
      padding: 5px 20px;
    }
  }
  /deep/ {
    .el-input__inner,
    .el-input__icon {
      height: 25px;
      line-height: 25px;
    }
  }
  .tree-select {
    display: inline-block;
    position: relative;
    width: 300px;
    vertical-align: middle;
    line-height: 25px;
    height: 25px;
    /deep/ {
      .vue-treeselect__control {
        height: 23px;
        .vue-treeselect__value-container {
          height: 23px;
          .vue-treeselect__single-value {
            line-height: 23px;
            height: 23px;
          }
        }
      }
    }
  }

  .view-icon {
    vertical-align: middle;
  }
  .edac-icon {
    color: $actionIconColor;
    font-size: 20px;
  }
  .edac-icon-setting1 {
    color: $settingColor;
  }
  .actions-icon {
    float: right;
    padding: 0 10px;
    .edac-icon {
      line-height: 25px;
      vertical-align: middle;
    }
  }
  .enquiry-title {
    line-height: 25px;
    display: inline-flex;
    .view-title {
      vertical-align: middle;
      padding: 0 5px;
      color: #606266;
    }
  }
  .table-row {
    /*height: calc(100% - 50px);*/
    min-height: 110px;
    flex: 1;

    /deep/ {
      .e-table {
        height: 100%;

        .el-table__body-wrapper {
          /*height: auto;*/
          /*height: calc(100% - 85px);*/
        }
        .el-table__empty-block {
          min-height: 30px;
        }
      }
    }
  }
}
.enquiry-general-budget {
  .filter-row {
    display: inline-block;
  }
}
</style>
