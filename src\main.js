import Vue from 'vue'

// import Cookies from 'js-cookie'
import storage from '@/utils/store'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import 'element-ui/lib/theme-chalk/display.css'

import '@/styles/index.scss' // global css
import '@/styles/reset.scss' // import common styles
import '@/icons/iconfont/iconfont.css' // iconfont

import App from './App'
import store from './store'
import router from './router'

import i18n from './lang' // Internationalization
import './icons' // icon
import './errorLog' // error log
import './permission' // permission control

import VueWorker from 'vue-worker'
Vue.use(VueWorker)

import Occupy from '@/components/Occupy'

import * as filters from './filters' // global filters
import VueBus from '@/utils/vueBus'

import 'xe-utils'
import VXETable from 'vxe-table'
import 'vxe-table/lib/index.css'
Vue.use(VXETable, {
  // Optional, automatically translate column headers, validation prompts, etc. in parameters (only valid for those that support internationalization)
  // translate: key => i18n.t(key),
  // Internationalize and translate built-in prompts for components
  i18n: key => i18n.t(key),
})

/* Global Components */
Vue.component('Occupy', Occupy)

import md5 from 'md5'
Vue.prototype.$md5 = md5

Vue.use(VueBus)

// Print function, please delete in production mode
Vue.prototype.print = (obj, type) => {
  let logType = 'log'
  if (type == null) {
    logType = 'log'
  } else if (parseInt(type) > -1) {
    // Number
    switch (type) {
      case 0:
        logType = 'log'
        break
      case 1:
        logType = 'table'
        break
      case 2:
        logType = 'info'
        break
      case 3:
        logType = 'warn'
        break
      case 4:
        logType = 'error'
        break
      default:
        logType = 'log'
        break
    }
  } else {
    logType = type
  }
  const log = JSON.parse(JSON.stringify(obj))
  console[logType](log)
}
Vue.use(Element, {
  size: storage.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value),
})

// register global utility filters.
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

/* Global Mixins */
import { globalMethods } from '@/views/mixins/global'
Vue.mixin(globalMethods)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App),
})
console.log('Environment Variable Test 1:', process.env.VUE_APP_TEST)
console.log('Environment Variable Test 2:', process.env.VUE_APP_TEST)
