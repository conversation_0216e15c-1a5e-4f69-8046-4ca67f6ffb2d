<template>
  <div>
    <div class="actions">
      <!--      <el-button type="primary" size="mini" class="new-row" @click="onAddRow">{{ $t('button.add') }}</el-button>-->
      <el-button
        type="primary"
        size="mini"
        class="new-row"
        icon="el-icon-plus"
        circle
        @click="onAddRow"
      />
    </div>
    <el-table :show-header="false" :data="selectedAccounts" v-bind="$attrs" style="width: 100%">
      <el-table-column :label="$t('table.index')" type="index" />
      <el-table-column :label="$t('table.account')" width="310">
        <template v-if="scope && scope.row" slot-scope="scope">
          <el-select v-model="scope.row.account_id" style="width: 300px">
            <el-option
              v-for="item in accounts"
              :key="item.account_id"
              :value="item.account_id"
              :label="`[${item.ac_code}] ${item[language === 'en' ? 'ac_name_en' : 'ac_name_cn']}`"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.action')" min-width="80">
        <template v-if="scope && scope.row" slot-scope="scope">
          <span>
            <i class="el-icon-delete" @click="removeRow(scope.row)" />
          </span>
        </template>
      </el-table-column>
      <!--        <template slot="empty">-->
      <!--          <span></span>-->
      <!--          <div class="bottom-action">-->
      <!--            <el-button type="primary" size="mini" class="new-row" @click="onAddRow">add</el-button>-->
      <!--  &lt;!&ndash;          <i class="el-icon-plus" @click="onAddRow"/>&ndash;&gt;-->
      <!--          </div>-->
      <!--        </template>-->
      <!--      <template slot="append">-->
      <!--        <div class="bottom-action">-->
      <!--          <el-button type="primary" size="mini" class="new-row" @click="onAddRow">add</el-button>-->
      <!--&lt;!&ndash;          <i class="el-icon-plus" @click="onAddRow"/>&ndash;&gt;-->
      <!--        </div>-->
      <!--      </template>-->
    </el-table>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { searchAccounts } from '@/api/master/account'

export default {
  name: 'BudgetAccountSelect',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    fyCode: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      accounts: [],
    }
  },
  computed: {
    ...mapGetters(['language']),
    selectedAccounts: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
  },
  created() {
    const fy_code = this.fyCode
    searchAccounts({ fy_code }).then(res => {
      this.accounts = res
    })
  },
  methods: {
    onAddRow() {
      const item = {
        account_id: '',
      }
      this.selectedAccounts.push(item)
    },
    removeRow(row) {
      const data_index = this.selectedAccounts.findIndex(i => i.account_id === row.account_id)
      data_index > -1 && this.selectedAccounts.splice(data_index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.actions {
  text-align: right;
  float: right;
  z-index: 1;
  position: absolute;
  right: 0;
  .new-row {
  }
}
.el-icon-delete {
  cursor: pointer;
}
.bottom-action {
  text-align: center;
  .new-row {
  }
}
/deep/ {
  .el-table__empty-block {
    min-height: 35px;
  }
}
</style>
