<template>
  <el-dialog
    v-if="addInnerVisible"
    v-loading="loading"
    width="35%"
    :title="t('addLedger')"
    :visible.sync="show"
    custom-class="EDAC"
    append-to-body
  >
    <div>
      <el-form
        ref="form"
        :inline="true"
        :model="formInline"
        label-width="100px"
        :show-message="false"
        :rules="rules"
        class="demo-form-inline"
      >
        <el-form-item :label="t('vc_date')" prop="ledger_date">
          <el-date-picker
            v-model="formInline.ledger_date"
            size="mini"
            prefix-icon=""
            type="date"
            style="width: 180px"
            class="bank-date-picker"
          />
        </el-form-item>
        <el-form-item :label="t('summary')" prop="particulars">
          <el-input v-model="formInline.particulars" style="width: 180px" />
        </el-form-item>
        <el-form-item :label="t('amount_dr')" prop="amount_dr" :rules="drRules">
          <el-input
            :value="formInline.amount_dr"
            style="width: 180px"
            @input="onInput($event, 'amount_dr')"
            @blur="inputBlur($event, 'amount_dr')"
          />
        </el-form-item>
        <el-form-item :label="t('amount_cr')" prop="amount_cr" :rules="crRules">
          <el-input
            :value="formInline.amount_cr"
            style="width: 180px"
            @input="onInput($event, 'amount_cr')"
            @blur="inputBlur($event, 'amount_cr')"
          />
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="show = false">
        {{ $t('button.back') }}
      </el-button>
      <el-button v-if="addPreType" type="primary" @click="handleSuccess('BEFORE')">
        {{ t('addPre') }}
      </el-button>
      <el-button type="primary" @click="handleSuccess('AFTER')">
        {{ t('addNext') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createFileLedgers } from '@/api/periodic/bankReconciliation'
import dateUtil from '@/utils/date'
import { amountFormat } from '@/utils'

export default {
  props: {
    addInnerVisible: {
      type: Boolean,
      default: false,
    },
    selectFileRow: {
      type: Object,
      default: () => {},
    },
    ai_document_file_ledger_id: {
      type: Number,
      default: 0,
    },
    addPreType: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      langKey: 'bankAiReconciliation.',
      show: false,
      loading: false,
      formInline: {
        ledger_date: '',
        particulars: '',
        cr_amount: '',
        dr_amount: '',
      },
      rules: {
        ledger_date: [{ required: true, trigger: 'blur' }],
        particulars: [{ required: true, trigger: 'blur' }],
        amount_cr: [{ required: true, trigger: 'blur' }],
        amount_dr: [{ required: true, trigger: 'blur' }],
      },
    }
  },
  computed: {
    accountId() {
      return this.form.selectedAcCode
        ? this.bankList.find(item => item.ac_code === this.form.selectedAcCode).account_id
        : ''
    },
    crRules() {
      if (!this.formInline.amount_dr && !this.formInline.amount_cr) {
        return [{ required: true, message: this.t('required'), trigger: 'blur' }]
      }
      if (this.formInline.amount_dr) {
        return []
      }
      return []
    },
    drRules() {
      if (!this.formInline.amount_dr && !this.formInline.amount_cr) {
        return [{ required: true, message: this.t('required'), trigger: 'blur' }]
      }
      if (this.formInline.amount_cr) {
        return []
      }
      return []
    },
  },
  watch: {
    addInnerVisible: {
      handler: function(val) {
        this.show = val
        Object.keys(this.formInline).forEach(key => {
          this.formInline[key] = ''
        })
      },
      deep: true,
    },
    show: {
      handler: function(val) {
        if (!val) {
          this.$emit('close', false)
        }
      },
      deep: true,
    },
    selectedAcCode: {
      handler: function(val) {
        this.form.selectedAcCode = val
      },
      deep: true,
    },
  },
  created() {},
  methods: {
    onInput(e, key) {
      this.$set(this.formInline, key, e)
    },
    inputBlur(e, type) {
      if (!e.target.value || isNaN(e.target.value)) {
        this.$set(this.formInline, type, '')
        return
      }
      const data = Number(e.target.value).toFixed(2)
      if (data <= 0) {
        return this.$set(this.formInline, type, '')
      }
      if (type === 'amount_dr' && this.formInline.amount_cr) {
        this.$set(this.formInline, 'amount_cr', '')
        this.$set(this.formInline, 'amount_dr', data)
        return
      } else if (type === 'amount_cr' && this.formInline.amount_dr) {
        this.$set(this.formInline, 'amount_dr', '')
        this.$set(this.formInline, 'amount_cr', data)
        return
      }
      console.log(data)

      this.$set(this.formInline, type, data)
    },
    changeBank(val) {
      console.log(val)
      this.$emit('changeBank', val)
    },
    async handleSuccess(insert_type) {
      if (this.loading) return
      this.loading = true

      try {
        this.$refs.form.validate(async valid => {
          if (valid) {
            const params = {
              insert_type,
              ai_document_file_id: this.selectFileRow.ai_document_file_id,
              ai_document_file_ledger_id: this.ai_document_file_ledger_id,
              ledger_date: dateUtil.format(this.formInline.ledger_date, 'yyyy-MM-dd'),
              particulars: this.formInline.particulars,
              cr_amount: this.formInline.amount_cr ? this.formInline.amount_cr : '0.00',
              dr_amount: this.formInline.amount_dr ? this.formInline.amount_dr : '0.00',
            }
            await createFileLedgers(params)
            this.$message({
              message: this.$t('message.success'),
              type: 'success',
            })
            this.$emit('createSuccess')
          }
        })
        this.loading = false
      } catch (err) {
        console.log(err)
      }
    },
    handleError() {
      this.$message({
        message: this.t('fail'),
        type: 'error',
      })
      this.loading = false
    },
    beforeUpload() {
      if (this.loading) {
        return false
      }
      this.loading = true
    },
  },
}
</script>

<style lang="scss" scoped>
.bank-date-picker {
  ::v-deep {
    .el-input__inner {
      height: 30px;
    }
  }
}
::v-deep {
  .el-upload {
    width: 100%;
    padding: 20px;
  }
  .el-upload-dragger {
    width: 100%;
    height: 100%;
    border: none;
  }
  .el-upload__tip {
    text-align: left;
  }
  .el-input__prefix,
  .el-input__suffix {
    top: 1px !important;
  }
}
.bank-select {
  padding: 0 20px;
}
.el-upload__text {
  height: 100px;
  align-content: center;
  border: 1px dashed #bbbbbb;
  border-radius: 10px;
}
</style>
